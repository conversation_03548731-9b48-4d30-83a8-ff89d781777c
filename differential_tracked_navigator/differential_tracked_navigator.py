#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
import math
import os
import time

from geometry_msgs.msg import Twist, PointStamped
from nav_msgs.msg import Odometry, OccupancyGrid
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2

# Try to import Trigger service, fallback if not available
try:
    from std_srvs.srv import Trigger
    TRIGGER_AVAILABLE = True
except ImportError:
    TRIGGER_AVAILABLE = False


class PIDController:
    """PID Controller implementation for vehicle control"""

    def __init__(self, kp=1.0, ki=0.0, kd=0.0, dt=0.1, max_integral=10.0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.dt = dt
        self.max_integral = max_integral

        self.prev_error = 0.0
        self.integral = 0.0
        self.last_time = None
        
    def compute(self, error, current_time=None):
        """Compute PID output given error input"""
        if current_time is None:
            current_time = time.time()
            
        if self.last_time is None:
            self.last_time = current_time
            self.dt = 0.1  # Default dt
            # First call, return proportional only to avoid derivative spike
            output = self.kp * error
            self.prev_error = error
            return output
        else:
            self.dt = current_time - self.last_time
            
        # Avoid division by zero and very small dt
        if self.dt <= 0.001:  # 1ms minimum
            self.dt = 0.1
            
        # Compute PID terms
        self.integral += error * self.dt

        # Apply integral windup protection
        self.integral = max(-self.max_integral, min(self.max_integral, self.integral))

        derivative = (error - self.prev_error) / self.dt if self.dt > 0 else 0.0
        
        # Calculate output
        output = self.kp * error + self.ki * self.integral + self.kd * derivative
        
        # Store values for next iteration
        self.prev_error = error
        self.last_time = current_time
        
        return output
    
    def reset(self):
        """Reset PID controller state"""
        self.prev_error = 0.0
        self.integral = 0.0
        self.last_time = None
        
    def set_gains(self, kp, ki, kd):
        """Update PID gains"""
        self.kp = kp
        self.ki = ki
        self.kd = kd


class VehicleParameters:
    """Configuration parameters for the differential tracked vehicle"""
    def __init__(self):
        self.wheelbase = 0.5  # Distance between front and rear axles (meters)
        self.track_width = 0.4  # Distance between left and right tracks (meters)
        self.max_linear_velocity = 2.0  # Maximum linear velocity (m/s)
        self.max_angular_velocity = 1.5  # Maximum angular velocity (rad/s)
        self.min_linear_velocity = 0.1  # Minimum linear velocity (m/s)
        self.wheel_radius = 0.1  # Wheel radius (meters)
        
        # Control parameters
        self.lookahead_distance = 1.0  # Pure pursuit lookahead distance (meters)
        self.goal_tolerance = 0.2  # Distance tolerance for reaching waypoints (meters)
        self.angular_tolerance = 0.1  # Angular tolerance for orientation (radians)
        self.control_frequency = 10.0  # Control loop frequency (Hz)
        
        # PID control parameters
        self.linear_kp = 2.0
        self.linear_ki = 0.1
        self.linear_kd = 0.05
        self.angular_kp = 3.0
        self.angular_ki = 0.2
        self.angular_kd = 0.1
        
        # Safety parameters
        self.obstacle_detection_range = 2.0  # Range for obstacle detection (meters)
        self.safety_stop_distance = 0.5  # Distance to stop before obstacles (meters)
        self.emergency_stop_distance = 0.3  # Emergency stop distance (meters)
        self.enable_obstacle_detection = False  # Enable/disable obstacle detection


class DifferentialTrackedNavigator(Node):
    """ROS2 Navigation Controller for Differential Tracked Vehicle"""
    
    def __init__(self):
        super().__init__('differential_tracked_navigator')
        
        # Initialize vehicle parameters
        self.vehicle_params = VehicleParameters()
        self._declare_parameters()
        self._load_parameters()
        
        # Initialize PID controllers with integral windup protection
        dt = 1.0 / self.vehicle_params.control_frequency
        max_integral = getattr(self.vehicle_params, 'max_integral_windup', 5.0)

        self.linear_pid = PIDController(
            self.vehicle_params.linear_kp,
            self.vehicle_params.linear_ki,
            self.vehicle_params.linear_kd,
            dt,
            max_integral
        )
        self.angular_pid = PIDController(
            self.vehicle_params.angular_kp,
            self.vehicle_params.angular_ki,
            self.vehicle_params.angular_kd,
            dt,
            max_integral
        )
        
        # State variables
        self.current_pose = None
        self.current_velocity = None
        self.waypoints = []
        self.current_waypoint_index = 0
        self.is_navigating = False
        self.obstacle_detected = False
        self.emergency_stop = False
        self.map_data = None
        
        # Odometry monitoring
        self.last_odometry_time = None
        self.odometry_timeout = 2.0  # seconds
        self.odometry_lost_warned = False
        self.startup_grace_period = 10.0  # seconds - grace period after startup
        self.node_start_time = self.get_clock().now()
        self.consecutive_bad_odom_count = 0
        self.max_consecutive_bad_odom = 3  # Allow some bad readings before stopping

        # Navigation recovery state
        self.navigation_paused_due_to_odom_loss = False
        self.was_navigating_before_odom_loss = False

        # Position jump detection
        self.previous_pose = None
        self.position_jump_threshold = 2.0  # meters
        self.large_position_jump_detected = False
        self.position_stabilization_count = 0
        self.required_stable_readings = 5  # Require 5 stable readings before trusting position

        # Initial positioning check
        self.initial_position_set = False
        
        # Additional state variables for angular velocity smoothing
        self.prev_angular_vel = 0.0
        
        # Recovery control state  
        self.recovery_mode = False
        self.recovery_start_time = None

        # Waypoint transition control state
        self.waypoint_transition_mode = False
        self.waypoint_transition_start_time = None
        self.waypoint_transition_duration = 2.0  # seconds of gentle control after waypoint switch
        
        # Initialize publishers and subscribers
        self._setup_publishers()
        self._setup_subscribers()
        self._setup_services()
        
        # Load waypoints from file
        self._load_waypoints_from_file()
        
        # Control timer
        control_period = 1.0 / self.vehicle_params.control_frequency
        self.control_timer = self.create_timer(control_period, self._control_loop)
        
        self.get_logger().info('Differential Tracked Navigator with PID control initialized')
    
    def _declare_parameters(self):
        """Declare ROS2 parameters for vehicle configuration"""
        self.declare_parameter('vehicle.wheelbase', self.vehicle_params.wheelbase)
        self.declare_parameter('vehicle.track_width', self.vehicle_params.track_width)
        self.declare_parameter('vehicle.max_linear_velocity', self.vehicle_params.max_linear_velocity)
        self.declare_parameter('vehicle.max_angular_velocity', self.vehicle_params.max_angular_velocity)
        self.declare_parameter('vehicle.min_linear_velocity', self.vehicle_params.min_linear_velocity)
        self.declare_parameter('vehicle.wheel_radius', self.vehicle_params.wheel_radius)
        
        self.declare_parameter('control.lookahead_distance', self.vehicle_params.lookahead_distance)
        self.declare_parameter('control.goal_tolerance', self.vehicle_params.goal_tolerance)
        self.declare_parameter('control.angular_tolerance', self.vehicle_params.angular_tolerance)
        self.declare_parameter('control.control_frequency', self.vehicle_params.control_frequency)
        self.declare_parameter('control.fast_turn_mode', False)
        
        # PID parameters
        self.declare_parameter('control.linear_kp', self.vehicle_params.linear_kp)
        self.declare_parameter('control.linear_ki', self.vehicle_params.linear_ki)
        self.declare_parameter('control.linear_kd', self.vehicle_params.linear_kd)
        self.declare_parameter('control.angular_kp', self.vehicle_params.angular_kp)
        self.declare_parameter('control.angular_ki', self.vehicle_params.angular_ki)
        self.declare_parameter('control.angular_kd', self.vehicle_params.angular_kd)

        # PID safety parameters
        self.declare_parameter('control.max_integral_windup', 5.0)
        self.declare_parameter('control.enable_integral_reset', True)
        
        self.declare_parameter('safety.obstacle_detection_range', self.vehicle_params.obstacle_detection_range)
        self.declare_parameter('safety.safety_stop_distance', self.vehicle_params.safety_stop_distance)
        self.declare_parameter('safety.emergency_stop_distance', self.vehicle_params.emergency_stop_distance)
        self.declare_parameter('safety.enable_obstacle_detection', self.vehicle_params.enable_obstacle_detection)

        # Odometry health monitoring parameters
        self.declare_parameter('odometry.enable_health_check', True)
        self.declare_parameter('odometry.timeout', 3.0)
        self.declare_parameter('odometry.startup_grace_period', 15.0)
        self.declare_parameter('odometry.max_consecutive_bad_readings', 5)
        self.declare_parameter('odometry.max_position_jump', 2.0)
        self.declare_parameter('odometry.max_velocity_bound', 5.0)
        self.declare_parameter('odometry.max_angular_velocity_bound', 5.0)

        # Navigation recovery parameters
        self.declare_parameter('navigation.auto_resume_after_odom_recovery', True)
        self.declare_parameter('navigation.recalculate_waypoint_on_recovery', True)
        self.declare_parameter('navigation.max_waypoint_skip_distance', 1.0)

        # Position jump detection parameters
        self.declare_parameter('navigation.enable_position_jump_detection', True)
        self.declare_parameter('navigation.position_jump_threshold', 2.0)
        self.declare_parameter('navigation.required_stable_readings', 5)
        self.declare_parameter('navigation.max_reasonable_distance', 20.0)
        self.declare_parameter('navigation.emergency_angular_limit', 0.5)
        self.declare_parameter('navigation.emergency_linear_limit', 0.5)

        # Recovery control parameters
        self.declare_parameter('navigation.recovery_duration', 3.0)
        self.declare_parameter('navigation.recovery_min_authority', 0.3)
        self.declare_parameter('navigation.recovery_angle_tolerance', 45.0)

        # Waypoint transition parameters
        self.declare_parameter('navigation.waypoint_transition_duration', 2.0)
        self.declare_parameter('navigation.waypoint_transition_min_authority', 0.4)

        self.declare_parameter('waypoints_file', 'point.txt')
    
    def _load_parameters(self):
        """Load parameters from ROS2 parameter server"""
        self.vehicle_params.wheelbase = self.get_parameter('vehicle.wheelbase').value
        self.vehicle_params.track_width = self.get_parameter('vehicle.track_width').value
        self.vehicle_params.max_linear_velocity = self.get_parameter('vehicle.max_linear_velocity').value
        self.vehicle_params.max_angular_velocity = self.get_parameter('vehicle.max_angular_velocity').value
        self.vehicle_params.min_linear_velocity = self.get_parameter('vehicle.min_linear_velocity').value
        self.vehicle_params.wheel_radius = self.get_parameter('vehicle.wheel_radius').value
        
        self.vehicle_params.lookahead_distance = self.get_parameter('control.lookahead_distance').value
        self.vehicle_params.goal_tolerance = self.get_parameter('control.goal_tolerance').value
        self.vehicle_params.angular_tolerance = self.get_parameter('control.angular_tolerance').value
        self.vehicle_params.control_frequency = self.get_parameter('control.control_frequency').value
        
        # Load fast turn mode
        self.fast_turn_mode = self.get_parameter('control.fast_turn_mode').value
        
        # Load PID parameters
        self.vehicle_params.linear_kp = self.get_parameter('control.linear_kp').value
        self.vehicle_params.linear_ki = self.get_parameter('control.linear_ki').value
        self.vehicle_params.linear_kd = self.get_parameter('control.linear_kd').value
        self.vehicle_params.angular_kp = self.get_parameter('control.angular_kp').value
        self.vehicle_params.angular_ki = self.get_parameter('control.angular_ki').value
        self.vehicle_params.angular_kd = self.get_parameter('control.angular_kd').value

        # Load PID safety parameters
        self.vehicle_params.max_integral_windup = self.get_parameter('control.max_integral_windup').value
        self.vehicle_params.enable_integral_reset = self.get_parameter('control.enable_integral_reset').value
        
        self.vehicle_params.obstacle_detection_range = self.get_parameter('safety.obstacle_detection_range').value
        self.vehicle_params.safety_stop_distance = self.get_parameter('safety.safety_stop_distance').value
        self.vehicle_params.emergency_stop_distance = self.get_parameter('safety.emergency_stop_distance').value
        self.vehicle_params.enable_obstacle_detection = self.get_parameter('safety.enable_obstacle_detection').value

        # Load odometry health monitoring parameters
        self.enable_odometry_health_check = self.get_parameter('odometry.enable_health_check').value
        self.odometry_timeout = self.get_parameter('odometry.timeout').value
        self.startup_grace_period = self.get_parameter('odometry.startup_grace_period').value
        self.max_consecutive_bad_odom = self.get_parameter('odometry.max_consecutive_bad_readings').value
        self.max_position_jump = self.get_parameter('odometry.max_position_jump').value
        self.max_velocity_bound = self.get_parameter('odometry.max_velocity_bound').value
        self.max_angular_velocity_bound = self.get_parameter('odometry.max_angular_velocity_bound').value

        # Load navigation recovery parameters
        self.auto_resume_after_odom_recovery = self.get_parameter('navigation.auto_resume_after_odom_recovery').value
        self.recalculate_waypoint_on_recovery = self.get_parameter('navigation.recalculate_waypoint_on_recovery').value
        self.max_waypoint_skip_distance = self.get_parameter('navigation.max_waypoint_skip_distance').value

        # Load position jump detection parameters
        self.enable_position_jump_detection = self.get_parameter('navigation.enable_position_jump_detection').value
        self.position_jump_threshold = self.get_parameter('navigation.position_jump_threshold').value
        self.required_stable_readings = self.get_parameter('navigation.required_stable_readings').value
        self.max_reasonable_distance = self.get_parameter('navigation.max_reasonable_distance').value
        self.emergency_angular_limit = self.get_parameter('navigation.emergency_angular_limit').value
        self.emergency_linear_limit = self.get_parameter('navigation.emergency_linear_limit').value

        # Load recovery control parameters
        self.recovery_duration = self.get_parameter('navigation.recovery_duration').value
        self.recovery_min_authority = self.get_parameter('navigation.recovery_min_authority').value
        self.recovery_angle_tolerance_deg = self.get_parameter('navigation.recovery_angle_tolerance').value

        # Load waypoint transition parameters
        self.waypoint_transition_duration = self.get_parameter('navigation.waypoint_transition_duration').value
        self.waypoint_transition_min_authority = self.get_parameter('navigation.waypoint_transition_min_authority').value

        self.waypoints_file = self.get_parameter('waypoints_file').value

        # Log safety check status
        if not self.enable_odometry_health_check:
            self.get_logger().warn('⚠️  ODOMETRY HEALTH CHECK DISABLED - No odometry loss detection')

        if not self.enable_position_jump_detection:
            self.get_logger().warn('⚠️  POSITION JUMP DETECTION DISABLED - No position jump protection')

        if not self.enable_odometry_health_check and not self.enable_position_jump_detection:
            self.get_logger().warn('⚠️  ALL SAFETY CHECKS DISABLED - Running in unrestricted mode')
        else:
            self.get_logger().info('Safety checks configured successfully')
    
    def _setup_publishers(self):
        """Setup ROS2 publishers"""
        # Vehicle control publisher
        self.cmd_vel_publisher = self.create_publisher(
            Twist, 
            '/cmd_vel', 
            10
        )
        
        # Waypoint publisher
        self.waypoint_publisher = self.create_publisher(
            PointStamped,
            '/way_point',
            10
        )
        
        self.get_logger().info('Publishers initialized')
    
    def _setup_subscribers(self):
        """Setup ROS2 subscribers"""
        # State estimation subscriber
        self.state_estimation_subscriber = self.create_subscription(
            Odometry,
            '/state_estimation',
            self._state_estimation_callback,
            10
        )
        
        # LiDAR data subscriber
        self.lidar_subscriber = self.create_subscription(
            PointCloud2,
            '/registered_scan',
            self._lidar_callback,
            10
        )
        
        # Map subscriber (for map_server integration)
        map_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            durability=DurabilityPolicy.TRANSIENT_LOCAL,
            depth=1
        )
        
        self.map_subscriber = self.create_subscription(
            OccupancyGrid,
            '/map',
            self._map_callback,
            map_qos
        )
        
        self.get_logger().info('Subscribers initialized')

    def _setup_services(self):
        """Setup ROS2 services"""
        if TRIGGER_AVAILABLE:
            # Service to manually resume navigation
            self.resume_navigation_service = self.create_service(
                Trigger,
                'resume_navigation',
                self._resume_navigation_callback
            )
            self.get_logger().info('Services initialized')
        else:
            self.get_logger().warn('std_srvs.srv.Trigger not available, manual resume service disabled')

    def _load_waypoints_from_file(self):
        """Load waypoints from point.txt file"""
        try:
            if not os.path.exists(self.waypoints_file):
                self.get_logger().warn(f'Waypoints file {self.waypoints_file} not found')
                return
            
            self.waypoints = []
            with open(self.waypoints_file, 'r') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    try:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            x = float(parts[0])
                            y = float(parts[1])
                            z = float(parts[2]) if len(parts) > 2 else 0.0
                            self.waypoints.append((x, y, z))
                        else:
                            self.get_logger().warn(f'Invalid waypoint format at line {line_num}: {line}')
                    except ValueError as e:
                        self.get_logger().warn(f'Error parsing waypoint at line {line_num}: {e}')
            
            self.get_logger().info(f'Loaded {len(self.waypoints)} waypoints from {self.waypoints_file}')
            
            if self.waypoints:
                self.is_navigating = True
                self._publish_current_waypoint()
            
        except Exception as e:
            self.get_logger().error(f'Error loading waypoints file: {e}')
    
    def _state_estimation_callback(self, msg: Odometry):
        """Callback for vehicle state estimation (localization)"""
        # If all checks are disabled, accept data directly
        if not self.enable_odometry_health_check and not self.enable_position_jump_detection:
            self.current_pose = msg.pose.pose
            self.current_velocity = msg.twist.twist
            self.last_odometry_time = self.get_clock().now()
            return

        # Validate odometry data quality before accepting (if health check enabled)
        data_valid = True
        if self.enable_odometry_health_check:
            data_valid = self._validate_odometry_data(msg)

        if data_valid:
            # Check for position jumps before accepting new pose (if jump detection enabled)
            position_ok = True
            if self.enable_position_jump_detection:
                position_ok = self._check_position_jump(msg.pose.pose)

            if position_ok:
                self.current_pose = msg.pose.pose
                self.current_velocity = msg.twist.twist

                # Update odometry timestamp and reset warning flag (if health check enabled)
                if self.enable_odometry_health_check:
                    self.last_odometry_time = self.get_clock().now()
                    self.consecutive_bad_odom_count = 0  # Reset bad count on good data

                    if self.odometry_lost_warned:
                        self.get_logger().info('Odometry data recovered')
                        self.odometry_lost_warned = False

                        # Trigger navigation recovery check in next control loop
                        if self.navigation_paused_due_to_odom_loss:
                            self.get_logger().info('Odometry recovered - navigation will resume shortly')
            else:
                # Position jump detected, don't update pose but count as bad data
                if self.enable_odometry_health_check:
                    self.consecutive_bad_odom_count += 1
                    self.get_logger().warn(f'Position jump detected, ignoring odometry update (count: {self.consecutive_bad_odom_count})')
        else:
            # Invalid data
            if self.enable_odometry_health_check:
                self.consecutive_bad_odom_count += 1
                self.get_logger().warn(f'Invalid odometry data received (count: {self.consecutive_bad_odom_count})')
    
    def _lidar_callback(self, msg: PointCloud2):
        """Callback for LiDAR data processing and obstacle detection"""
        # Skip obstacle detection if disabled
        if not self.vehicle_params.enable_obstacle_detection:
            self.obstacle_detected = False
            self.emergency_stop = False
            return
            
        try:
            self.obstacle_detected = False
            self.emergency_stop = False
            
            # Convert PointCloud2 to list of points
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
            
            # Check for obstacles in the safety zone
            for point in points:
                x, y, _ = point  # Ignore z coordinate for 2D navigation
                distance = math.sqrt(x**2 + y**2)
                
                # Check if point is within detection range and in front of vehicle
                if (distance < self.vehicle_params.obstacle_detection_range and 
                    x > 0 and  # Only consider points in front
                    abs(y) < self.vehicle_params.track_width / 2 + 0.2):  # Within vehicle width plus margin
                    
                    if distance < self.vehicle_params.emergency_stop_distance:
                        self.emergency_stop = True
                        break
                    elif distance < self.vehicle_params.safety_stop_distance:
                        self.obstacle_detected = True
            
        except Exception as e:
            self.get_logger().warn(f'Error processing LiDAR data: {e}')
    
    def _map_callback(self, msg: OccupancyGrid):
        """Callback for map data"""
        self.map_data = msg
        self.get_logger().info('Map data received')
    
    def _publish_current_waypoint(self):
        """Publish the current target waypoint"""
        if self.current_waypoint_index < len(self.waypoints):
            waypoint = self.waypoints[self.current_waypoint_index]
            
            point_msg = PointStamped()
            point_msg.header.stamp = self.get_clock().now().to_msg()
            point_msg.header.frame_id = 'map'
            point_msg.point.x = float(waypoint[0])
            point_msg.point.y = float(waypoint[1])
            point_msg.point.z = float(waypoint[2])
            
            self.waypoint_publisher.publish(point_msg)
    
    def _control_loop(self):
        """Main control loop for navigation"""
        # Check for odometry data loss (only if enabled)
        if self.enable_odometry_health_check:
            if not self._check_odometry_health():
                # CRITICAL: Reset PID controllers when odometry is lost to prevent control instability
                if self.is_navigating and not self.navigation_paused_due_to_odom_loss:
                    self.get_logger().warn('Pausing navigation due to odometry loss')
                    self.was_navigating_before_odom_loss = True
                    self.navigation_paused_due_to_odom_loss = True

                self._reset_pid_controllers()
                self._stop_vehicle()
                return

        # Check if we should resume navigation after odometry recovery
        if (self.navigation_paused_due_to_odom_loss and
            self.was_navigating_before_odom_loss and
            self.auto_resume_after_odom_recovery):

            self.get_logger().info('Resuming navigation after odometry recovery')
            self.navigation_paused_due_to_odom_loss = False
            self.was_navigating_before_odom_loss = False

            # Enter recovery mode for gentle control
            self.recovery_mode = True
            self.recovery_start_time = self.get_clock().now()

            # Reset PID controllers for clean restart
            self._reset_pid_controllers()

            # Optionally recalculate closest waypoint after position jump
            if self.recalculate_waypoint_on_recovery:
                self._recalculate_target_waypoint()

        if not self.is_navigating or not self.current_pose or not self.waypoints:
            return
            
        # Initial position check - warn if starting position is very far from first waypoint
        if not self.initial_position_set and self.current_pose:
            first_waypoint = self.waypoints[0]
            current_x = self.current_pose.position.x
            current_y = self.current_pose.position.y
            initial_distance = math.sqrt((first_waypoint[0] - current_x)**2 + (first_waypoint[1] - current_y)**2)
            
            if initial_distance > 10.0:  # More than 10m from first waypoint
                self.get_logger().warn(
                    f'Initial position is far from first waypoint: {initial_distance:.2f}m. '
                    f'Current: ({current_x:.2f}, {current_y:.2f}), '
                    f'First waypoint: ({first_waypoint[0]:.2f}, {first_waypoint[1]:.2f})'
                )
            self.initial_position_set = True

        # Additional safety check: Don't navigate if position seems unreliable (only if detection enabled)
        if self.enable_position_jump_detection and self.large_position_jump_detected:
            self.get_logger().debug('Position unstable, skipping navigation control')
            return
        
        # Check for emergency stop first
        if self.vehicle_params.enable_obstacle_detection and self.emergency_stop:
            self.get_logger().warn('Emergency stop activated due to obstacle detection!')
            self._stop_vehicle()
            return
        
        try:
            # Check if we've reached the current waypoint
            if self._reached_current_waypoint():
                self.current_waypoint_index += 1

                # Reset PID controllers for new waypoint
                self.linear_pid.reset()
                self.angular_pid.reset()

                if self.current_waypoint_index >= len(self.waypoints):
                    # Completed all waypoints
                    self._stop_vehicle()
                    self.is_navigating = False
                    self.get_logger().info('Navigation completed - all waypoints reached')
                    return
                else:
                    # Enter waypoint transition mode for gentle control
                    self.waypoint_transition_mode = True
                    self.waypoint_transition_start_time = self.get_clock().now()

                    self.get_logger().info(f'Moving to waypoint {self.current_waypoint_index + 1}/{len(self.waypoints)} (transition mode)')
                    self._publish_current_waypoint()
            
            # Calculate control commands using PID
            cmd_vel = self._calculate_control_command()
            
            # Apply safety constraints
            if self.vehicle_params.enable_obstacle_detection and self.obstacle_detected:
                self.get_logger().warn('Obstacle detected - stopping vehicle')
                self._stop_vehicle()
            else:
                self.cmd_vel_publisher.publish(cmd_vel)
            
        except Exception as e:
            self.get_logger().error(f'Error in control loop: {e}')
            # Reset PID controllers on any error to prevent instability
            self._reset_pid_controllers()
            self._stop_vehicle()
    
    def _reached_current_waypoint(self) -> bool:
        """Check if the vehicle has reached the current waypoint"""
        if self.current_waypoint_index >= len(self.waypoints):
            return False
        
        waypoint = self.waypoints[self.current_waypoint_index]
        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y
        
        distance = math.sqrt((waypoint[0] - current_x)**2 + (waypoint[1] - current_y)**2)
        return distance < self.vehicle_params.goal_tolerance
    
    def _calculate_control_command(self) -> Twist:
        """Calculate control command using PID controllers"""
        if self.current_waypoint_index >= len(self.waypoints):
            return Twist()

        # Safety check: Don't generate commands if position is unstable (only if detection enabled)
        if self.enable_position_jump_detection and self.large_position_jump_detected:
            self.get_logger().debug('Position unstable, sending zero command')
            return Twist()

        waypoint = self.waypoints[self.current_waypoint_index]
        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y

        # Calculate current yaw from quaternion
        current_yaw = self._quaternion_to_yaw(self.current_pose.orientation)

        # Calculate distance and angle to waypoint
        dx = waypoint[0] - current_x
        dy = waypoint[1] - current_y
        distance_to_goal = math.sqrt(dx**2 + dy**2)

        # Safety check: If distance is unreasonably large, something is wrong (only if detection enabled)
        if self.enable_position_jump_detection and distance_to_goal > self.max_reasonable_distance:
            self.get_logger().warn(
                f'Distance to goal is unreasonably large: {distance_to_goal:.2f}m. '
                f'Possible odometry error. Sending zero command.'
            )
            return Twist()
        
        # Calculate target heading
        target_yaw = math.atan2(dy, dx)
        yaw_error = self._normalize_angle(target_yaw - current_yaw)
        
        # PID control implementation
        cmd_vel = Twist()
        
        # Check if we're in recovery mode or waypoint transition mode
        control_factor = 1.0
        current_time = self.get_clock().now()

        if self.recovery_mode:
            recovery_elapsed = (current_time - self.recovery_start_time).nanoseconds / 1e9

            if recovery_elapsed < self.recovery_duration:
                # Gradually increase control authority during recovery
                control_factor = min(1.0, recovery_elapsed / self.recovery_duration)
                control_factor = max(self.recovery_min_authority, control_factor)
                self.get_logger().debug(f'Recovery mode: factor={control_factor:.2f}, elapsed={recovery_elapsed:.1f}s')
            else:
                # Recovery period complete
                self.recovery_mode = False
                self.get_logger().info('Recovery mode complete, resuming full control authority')

        elif self.waypoint_transition_mode:
            transition_elapsed = (current_time - self.waypoint_transition_start_time).nanoseconds / 1e9

            if transition_elapsed < self.waypoint_transition_duration:
                # Gradually increase control authority during waypoint transition
                control_factor = min(1.0, transition_elapsed / self.waypoint_transition_duration)
                control_factor = max(self.waypoint_transition_min_authority, control_factor)
                self.get_logger().debug(f'Waypoint transition: factor={control_factor:.2f}, elapsed={transition_elapsed:.1f}s')
            else:
                # Transition period complete
                self.waypoint_transition_mode = False
                self.get_logger().info('Waypoint transition complete, resuming full control authority')

        # Angular velocity using PID control
        angular_vel = self.angular_pid.compute(yaw_error)
        
        # Apply fast turn mode boost for large yaw errors
        if self.fast_turn_mode and abs(yaw_error) > math.pi / 6:  # > 30 degrees
            # Boost angular response for faster turning
            boost_factor = min(1.5, 1.0 + abs(yaw_error) / math.pi)  # Up to 1.5x boost
            angular_vel *= boost_factor
            self.get_logger().debug(f'Fast turn mode: yaw_error={math.degrees(yaw_error):.1f}°, boost={boost_factor:.2f}')

        # Apply control factor to limit aggressive control during transitions
        if self.recovery_mode or self.waypoint_transition_mode:
            angular_vel *= control_factor

        # Safety limit for angular velocity based on error magnitude
        if abs(yaw_error) > math.pi:  # Error > 180 degrees indicates possible odometry issue
            self.get_logger().warn(f'Large yaw error detected: {yaw_error:.2f} rad. Limiting angular velocity.')
            angular_vel = math.copysign(min(abs(angular_vel), self.emergency_angular_limit), angular_vel)

        # Adjust angle tolerance based on recovery mode and distance
        base_angle_tolerance = math.pi / 4  # 45 degrees default
        
        # Be more permissive for close waypoints to avoid getting stuck
        if distance_to_goal < 3.0:  # Within 3 meters
            distance_factor = max(0.5, distance_to_goal / 3.0)
            angle_tolerance = base_angle_tolerance + (math.pi / 3) * (1.0 - distance_factor)  # Up to 105 degrees when very close
        else:
            angle_tolerance = base_angle_tolerance
            
        if self.recovery_mode:
            # More permissive angle tolerance during recovery
            angle_tolerance = max(angle_tolerance, math.radians(self.recovery_angle_tolerance_deg))

        # Linear velocity using PID control - only move forward if roughly aligned
        if abs(yaw_error) < angle_tolerance:
            linear_vel = self.linear_pid.compute(distance_to_goal)
            
            self.get_logger().debug(
                f'Aligned: yaw_error={math.degrees(yaw_error):.1f}°, '
                f'tolerance={math.degrees(angle_tolerance):.1f}°, '
                f'distance={distance_to_goal:.2f}m, linear_vel={linear_vel:.2f}'
            )

            # Apply control factor to linear velocity too
            if self.recovery_mode or self.waypoint_transition_mode:
                linear_vel *= control_factor

            # Safety limit for linear velocity based on distance
            # Use adaptive threshold based on waypoint spacing
            adaptive_threshold = min(self.max_reasonable_distance / 2, 20.0)  # Cap at 20m
            if distance_to_goal > adaptive_threshold:
                self.get_logger().warn(
                    f'Large distance error: {distance_to_goal:.2f}m (threshold: {adaptive_threshold:.1f}m). '
                    f'Current waypoint: {self.current_waypoint_index + 1}/{len(self.waypoints)}. '
                    f'Limiting linear velocity to {self.emergency_linear_limit:.1f} m/s.'
                )
                linear_vel = min(linear_vel, self.emergency_linear_limit)

            # Reduce speed when turning (less aggressive speed reduction)
            speed_factor = max(0.5, 1.0 - abs(yaw_error) / (math.pi / 3))  # More forgiving speed factor
            linear_vel *= speed_factor
        else:
            # Not aligned - need to turn first, but allow some forward motion to avoid getting stuck
            if distance_to_goal > 3.0:  # Far from target - allow forward motion while turning
                linear_vel = 0.4  # Moderate forward motion during turning
            elif distance_to_goal > 1.0:  # Medium distance - slow forward motion
                linear_vel = 0.2  # Slow forward motion during turning
            else:
                # Very close - be more careful but don't stop completely
                if abs(yaw_error) < math.pi / 2:  # Less than 90 degrees
                    linear_vel = 0.1  # Very slow forward motion
                else:
                    linear_vel = 0.0  # Stop only for very large angle errors when very close

            # In recovery or transition mode, allow some forward motion even when not perfectly aligned
            if (self.recovery_mode or self.waypoint_transition_mode) and abs(yaw_error) < math.pi / 2:  # 90 degrees
                linear_vel = max(linear_vel, 0.1 * control_factor)  # Very slow forward motion
                
            self.get_logger().debug(
                f'Not aligned: yaw_error={math.degrees(yaw_error):.1f}°, '
                f'distance={distance_to_goal:.2f}m, linear_vel={linear_vel:.2f}'
            )
        
        # Apply velocity limits
        cmd_vel.linear.x = max(
            -self.vehicle_params.max_linear_velocity,
            min(
                self.vehicle_params.max_linear_velocity,
                linear_vel
            )
        )
        
        cmd_vel.angular.z = max(
            -self.vehicle_params.max_angular_velocity,
            min(
                self.vehicle_params.max_angular_velocity,
                angular_vel
            )
        )
        
        # Apply angular velocity change rate limiting for smoother control
        max_angular_acceleration = 5.0  # rad/s² - increased for faster response
        dt = 1.0 / self.vehicle_params.control_frequency
        max_angular_change = max_angular_acceleration * dt
        
        angular_change = cmd_vel.angular.z - self.prev_angular_vel
        if abs(angular_change) > max_angular_change:
            cmd_vel.angular.z = self.prev_angular_vel + math.copysign(max_angular_change, angular_change)
            self.get_logger().debug(f'Angular velocity rate limited: change={angular_change:.3f}, max={max_angular_change:.3f}')
        
        # Update previous angular velocity for next iteration
        self.prev_angular_vel = cmd_vel.angular.z
        
        # Apply distance-based angular velocity scaling for smoother approach
        # Only apply significant scaling when very close to target
        if distance_to_goal < 1.5:  # Within 1.5 meters of target (reduced from 3.0)
            distance_factor = max(0.5, distance_to_goal / 1.5)  # Scale down to minimum 50% (increased from 30%)
            cmd_vel.angular.z *= distance_factor
            self.get_logger().debug(f'Distance-based angular scaling: factor={distance_factor:.2f}, distance={distance_to_goal:.2f}m')
        
        # Apply minimum velocity constraint when moving forward
        # Skip minimum velocity constraint in recovery/transition modes or when turning significantly
        skip_min_velocity = (self.recovery_mode or self.waypoint_transition_mode or 
                             abs(yaw_error) > math.pi / 6)  # 30 degrees
        
        if (cmd_vel.linear.x > 0 and 
            cmd_vel.linear.x < self.vehicle_params.min_linear_velocity and
            not skip_min_velocity):
            cmd_vel.linear.x = self.vehicle_params.min_linear_velocity

        # Final safety check: ensure no NaN or infinite values
        if not (math.isfinite(cmd_vel.linear.x) and math.isfinite(cmd_vel.angular.z)):
            self.get_logger().error('Invalid control command detected! Sending stop command.')
            self._reset_pid_controllers()
            return Twist()  # Return zero velocity
            
        # Emergency fallback: if we're very close to target but not moving, ensure some motion
        if (distance_to_goal < 5.0 and abs(cmd_vel.linear.x) < 0.05 and 
            abs(yaw_error) < math.pi and not self.obstacle_detected):
            # We're close, not moving much, angle isn't too crazy, and no obstacles
            # Give a small push to prevent getting stuck
            if abs(yaw_error) > math.pi / 6:  # 30 degrees
                # Turn in place with small forward motion
                cmd_vel.linear.x = 0.1
                self.get_logger().debug(f'Emergency nudge: distance={distance_to_goal:.2f}m, yaw_error={math.degrees(yaw_error):.1f}°')

        # Log detailed control info when linear velocity is very low
        if abs(cmd_vel.linear.x) < 0.05:
            self.get_logger().info(
                f'Low/zero linear velocity detected: '
                f'waypoint={self.current_waypoint_index + 1}/{len(self.waypoints)}, '
                f'distance={distance_to_goal:.2f}m, yaw_error={math.degrees(yaw_error):.1f}°, '
                f'angle_tolerance={math.degrees(angle_tolerance):.1f}°, '
                f'linear_vel={cmd_vel.linear.x:.3f}, angular_vel={cmd_vel.angular.z:.3f}'
            )

        # Log control commands during recovery or transition for debugging
        if self.recovery_mode:
            self.get_logger().info(
                f'Recovery control: yaw_error={yaw_error:.3f}rad ({math.degrees(yaw_error):.1f}°), '
                f'distance={distance_to_goal:.2f}m, linear={cmd_vel.linear.x:.3f}, angular={cmd_vel.angular.z:.3f}, '
                f'factor={control_factor:.2f}'
            )
        elif self.waypoint_transition_mode:
            self.get_logger().info(
                f'Waypoint transition: waypoint={self.current_waypoint_index + 1}, '
                f'yaw_error={yaw_error:.3f}rad ({math.degrees(yaw_error):.1f}°), '
                f'distance={distance_to_goal:.2f}m, linear={cmd_vel.linear.x:.3f}, angular={cmd_vel.angular.z:.3f}, '
                f'factor={control_factor:.2f}'
            )

        return cmd_vel
    
    def _stop_vehicle(self):
        """Send stop command to vehicle and ensure it's published multiple times for safety"""
        stop_msg = Twist()
        stop_msg.linear.x = 0.0
        stop_msg.angular.z = 0.0

        # Publish stop command multiple times to ensure it's received
        for _ in range(3):
            self.cmd_vel_publisher.publish(stop_msg)

    def _reset_pid_controllers(self):
        """Reset PID controllers to prevent integral windup and control instability"""
        self.linear_pid.reset()
        self.angular_pid.reset()
        self.get_logger().info('PID controllers reset due to odometry loss')

    def _recalculate_target_waypoint(self):
        """Recalculate the target waypoint after odometry recovery"""
        if not self.current_pose or not self.waypoints:
            return

        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y

        # Find the closest waypoint ahead of current position
        min_distance = float('inf')
        best_waypoint_index = self.current_waypoint_index

        # Check current and future waypoints
        for i in range(self.current_waypoint_index, len(self.waypoints)):
            waypoint = self.waypoints[i]
            distance = math.sqrt((waypoint[0] - current_x)**2 + (waypoint[1] - current_y)**2)

            if distance < min_distance:
                min_distance = distance
                best_waypoint_index = i

        # If we're very close to a future waypoint, skip to it
        skip_threshold = min(self.max_waypoint_skip_distance, self.vehicle_params.goal_tolerance * 3)
        if best_waypoint_index != self.current_waypoint_index and min_distance < skip_threshold:
            old_index = self.current_waypoint_index
            self.current_waypoint_index = best_waypoint_index
            self.get_logger().info(
                f'Adjusted target waypoint from {old_index + 1} to {self.current_waypoint_index + 1} '
                f'(distance: {min_distance:.2f}m) after odometry recovery'
            )
            self._publish_current_waypoint()
        else:
            self.get_logger().info(
                f'Continuing with waypoint {self.current_waypoint_index + 1} '
                f'(distance: {min_distance:.2f}m) after odometry recovery'
            )
    
    def _quaternion_to_yaw(self, quat) -> float:
        """Convert quaternion to yaw angle"""
        siny_cosp = 2 * (quat.w * quat.z + quat.x * quat.y)
        cosy_cosp = 1 - 2 * (quat.y * quat.y + quat.z * quat.z)
        return math.atan2(siny_cosp, cosy_cosp)
    
    def _normalize_angle(self, angle: float) -> float:
        """Normalize angle to [-pi, pi]"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def _check_odometry_health(self) -> bool:
        """Check if odometry data is being received within timeout period"""
        current_time = self.get_clock().now()

        # Check if we're still in startup grace period
        time_since_startup = (current_time - self.node_start_time).nanoseconds / 1e9
        if time_since_startup < self.startup_grace_period:
            if self.last_odometry_time is None:
                # Still in grace period, just warn but don't stop
                if not self.odometry_lost_warned:
                    self.get_logger().info(f'Waiting for odometry data... ({time_since_startup:.1f}s/{self.startup_grace_period}s)')
                    self.odometry_lost_warned = True
                return False  # Don't navigate yet, but don't stop either

        # After grace period, check for odometry data
        if self.last_odometry_time is None:
            if not self.odometry_lost_warned:
                self.get_logger().warn('No odometry data received after grace period')
                self.odometry_lost_warned = True
            # Don't call _stop_vehicle() here to avoid recursion
            return False

        # Check for data timeout
        time_since_last_odom = (current_time - self.last_odometry_time).nanoseconds / 1e9

        # Use dynamic timeout based on control frequency
        dynamic_timeout = max(self.odometry_timeout, 3.0 / self.vehicle_params.control_frequency)

        if time_since_last_odom > dynamic_timeout:
            if not self.odometry_lost_warned:
                self.get_logger().warn(f'Odometry data lost! No data received for {time_since_last_odom:.2f} seconds')
                self.odometry_lost_warned = True
            # Don't call _stop_vehicle() here to avoid recursion
            return False

        # Check for consecutive bad odometry readings
        if self.consecutive_bad_odom_count >= self.max_consecutive_bad_odom:
            if not self.odometry_lost_warned:
                self.get_logger().warn(f'Too many consecutive bad odometry readings: {self.consecutive_bad_odom_count}')
                self.odometry_lost_warned = True
            # Don't call _stop_vehicle() here to avoid recursion
            return False

        return True

    def _validate_odometry_data(self, msg: Odometry) -> bool:
        """Validate odometry data quality to prevent using corrupted data"""
        try:
            # Check for NaN or infinite values in position
            pose = msg.pose.pose.position
            if not (math.isfinite(pose.x) and math.isfinite(pose.y) and math.isfinite(pose.z)):
                self.get_logger().warn('Invalid position data: NaN or infinite values detected')
                return False

            # Check for reasonable position bounds (configurable)
            max_position = 1000.0  # meters - could be made configurable
            if (abs(pose.x) > max_position or abs(pose.y) > max_position or abs(pose.z) > max_position):
                self.get_logger().warn(f'Position out of bounds: x={pose.x:.2f}, y={pose.y:.2f}, z={pose.z:.2f}')
                return False

            # Check orientation quaternion validity
            quat = msg.pose.pose.orientation
            if not (math.isfinite(quat.x) and math.isfinite(quat.y) and
                   math.isfinite(quat.z) and math.isfinite(quat.w)):
                self.get_logger().warn('Invalid orientation data: NaN or infinite values detected')
                return False

            # Check quaternion normalization (should be close to 1.0)
            quat_norm = math.sqrt(quat.x**2 + quat.y**2 + quat.z**2 + quat.w**2)
            if abs(quat_norm - 1.0) > 0.1:  # Allow some tolerance
                self.get_logger().warn(f'Quaternion not normalized: norm={quat_norm:.3f}')
                return False

            # Check velocity data
            vel = msg.twist.twist.linear
            ang_vel = msg.twist.twist.angular
            max_velocity = self.max_velocity_bound  # Use configurable parameter
            max_angular_velocity = self.max_angular_velocity_bound  # Use configurable parameter

            if not (math.isfinite(vel.x) and math.isfinite(vel.y) and math.isfinite(vel.z)):
                self.get_logger().warn('Invalid linear velocity data')
                return False

            if not (math.isfinite(ang_vel.x) and math.isfinite(ang_vel.y) and math.isfinite(ang_vel.z)):
                self.get_logger().warn('Invalid angular velocity data')
                return False

            if (abs(vel.x) > max_velocity or abs(vel.y) > max_velocity or
                abs(ang_vel.z) > max_angular_velocity):
                self.get_logger().warn(f'Velocity out of bounds: linear={vel.x:.2f}, angular={ang_vel.z:.2f}')
                return False

            # Check for position jumps (if we have previous data)
            if hasattr(self, 'current_pose') and self.current_pose is not None:
                prev_pose = self.current_pose.position
                dx = pose.x - prev_pose.x
                dy = pose.y - prev_pose.y
                distance_jump = math.sqrt(dx**2 + dy**2)

                # Maximum allowed position jump per update (configurable parameter)
                max_jump = self.max_position_jump

                if distance_jump > max_jump:
                    self.get_logger().warn(f'Large position jump detected: {distance_jump:.2f}m (max: {max_jump:.2f}m)')
                    return False

            return True

        except Exception as e:
            self.get_logger().error(f'Error validating odometry data: {e}')
            return False

    def _resume_navigation_callback(self, request, response):
        """Service callback to manually resume navigation"""
        try:
            if self.navigation_paused_due_to_odom_loss:
                self.get_logger().info('Manually resuming navigation via service call')
                self.navigation_paused_due_to_odom_loss = False
                self.was_navigating_before_odom_loss = False

                # Reset PID controllers for clean restart
                self._reset_pid_controllers()

                # Optionally recalculate closest waypoint
                if self.recalculate_waypoint_on_recovery:
                    self._recalculate_target_waypoint()

                response.success = True
                response.message = "Navigation resumed successfully"
                self.get_logger().info('Navigation manually resumed')
            else:
                response.success = False
                response.message = "Navigation is not paused due to odometry loss"
                self.get_logger().warn('Resume navigation called but navigation is not paused')

        except Exception as e:
            response.success = False
            response.message = f"Failed to resume navigation: {str(e)}"
            self.get_logger().error(f'Error resuming navigation: {e}')

        return response

    def _check_position_jump(self, new_pose) -> bool:
        """Check for large position jumps that might indicate odometry errors"""
        if self.previous_pose is None:
            # First pose reading, accept it
            self.previous_pose = new_pose
            self.position_stabilization_count = 1
            return True

        # Calculate position change
        dx = new_pose.position.x - self.previous_pose.position.x
        dy = new_pose.position.y - self.previous_pose.position.y
        position_change = math.sqrt(dx**2 + dy**2)

        # Check if position change is reasonable (use configured threshold)
        max_reasonable_change = self.position_jump_threshold

        if position_change > max_reasonable_change:
            # Large position jump detected
            self.large_position_jump_detected = True
            self.position_stabilization_count = 0
            self.get_logger().warn(
                f'Large position jump detected: {position_change:.2f}m '
                f'(threshold: {max_reasonable_change:.2f}m)'
            )
            return False
        else:
            # Position change is reasonable
            if self.large_position_jump_detected:
                # We're recovering from a position jump
                self.position_stabilization_count += 1

                if self.position_stabilization_count >= self.required_stable_readings:
                    # Position has stabilized
                    self.large_position_jump_detected = False
                    self.position_stabilization_count = 0
                    self.get_logger().info('Position stabilized after jump, resuming normal operation')
                    # Reset PID controllers to prevent using accumulated errors
                    self._reset_pid_controllers()
                else:
                    self.get_logger().info(
                        f'Position stabilizing: {self.position_stabilization_count}/{self.required_stable_readings}'
                    )

            self.previous_pose = new_pose
            return not self.large_position_jump_detected


def main(args=None):
    """Main function to run the navigation node"""
    rclpy.init(args=args)
    
    try:
        navigator = DifferentialTrackedNavigator()
        rclpy.spin(navigator)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error running navigator: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()