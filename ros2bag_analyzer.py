#!/usr/bin/env python3

"""
ROS2 Bag Data Analyzer for PID Control Parameter Tuning

This script reads ROS2 bag files, extracts topic data to CSV files,
and generates trajectory error analysis plots for PID parameter tuning.

Author: Generated for differential tracked navigator PID tuning
"""

import os
import sys
import argparse
import math
import sqlite3
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta

try:
    import rclpy
    from rclpy.serialization import deserialize_message
    from rosidl_runtime_py.utilities import get_message
    import rosbag2_py
except ImportError:
    print("Error: ROS2 packages not found. Please ensure ROS2 is installed and sourced.")
    sys.exit(1)


class ROS2BagAnalyzer:
    """Analyzer for ROS2 bag files with PID tuning focus"""
    
    def __init__(self, bag_path: str, output_dir: str = "analysis_output"):
        """
        Initialize the analyzer
        
        Args:
            bag_path: Path to the ROS2 bag file
            output_dir: Directory to save CSV files and plots
        """
        self.bag_path = bag_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Data storage
        self.topic_data = {}
        self.csv_files = {}
        
        # Default topics of interest for navigation PID tuning
        self.topics_of_interest = {
            '/state_estimation': 'nav_msgs/msg/Odometry',
            '/way_point': 'geometry_msgs/msg/PointStamped', 
            '/cmd_vel': 'geometry_msgs/msg/Twist',
            '/registered_scan': 'sensor_msgs/msg/PointCloud2'  # Optional for obstacle analysis
        }
        
        print(f"Initialized ROS2BagAnalyzer for: {bag_path}")
        print(f"Output directory: {self.output_dir}")

    def read_bag_file(self) -> Dict:
        """
        Read ROS2 bag file and extract message data
        
        Returns:
            Dictionary containing extracted topic data
        """
        print("Reading ROS2 bag file...")
        
        # Initialize rosbag2 reader
        storage_options = rosbag2_py.StorageOptions(uri=self.bag_path, storage_id='sqlite3')
        converter_options = rosbag2_py.ConverterOptions(
            input_serialization_format='cdr',
            output_serialization_format='cdr'
        )
        
        reader = rosbag2_py.SequentialReader()
        reader.open(storage_options, converter_options)
        
        # Get bag metadata
        metadata = reader.get_metadata()
        print(f"Bag duration: {metadata.duration.nanoseconds / 1e9:.2f} seconds")
        print(f"Message count: {metadata.message_count}")
        
        # Get available topics
        available_topics = {info.name: info.type for info in metadata.topics_and_types}
        print(f"Available topics: {list(available_topics.keys())}")
        
        # Initialize data storage for each topic
        for topic_name in self.topics_of_interest:
            if topic_name in available_topics:
                self.topic_data[topic_name] = []
                print(f"Will extract: {topic_name}")
            else:
                print(f"Warning: Topic {topic_name} not found in bag")
        
        # Read messages
        message_count = 0
        while reader.has_next():
            (topic, data, timestamp) = reader.read_next()
            
            if topic in self.topic_data:
                # Get message type and deserialize
                msg_type = get_message(available_topics[topic])
                msg = deserialize_message(data, msg_type)
                
                # Store message with timestamp
                self.topic_data[topic].append({
                    'timestamp': timestamp,
                    'message': msg
                })
                
                message_count += 1
                if message_count % 1000 == 0:
                    print(f"Processed {message_count} messages...")
        
        reader.close()
        print(f"Finished reading bag file. Extracted {message_count} messages.")
        return self.topic_data

    def export_to_csv(self) -> Dict[str, str]:
        """
        Export topic data to CSV files
        
        Returns:
            Dictionary mapping topic names to CSV file paths
        """
        print("Exporting data to CSV files...")
        
        for topic_name, messages in self.topic_data.items():
            if not messages:
                continue
                
            csv_file = self.output_dir / f"{topic_name.replace('/', '_')}.csv"
            self.csv_files[topic_name] = str(csv_file)
            
            # Convert messages to DataFrame based on message type
            df = self._messages_to_dataframe(topic_name, messages)
            
            if df is not None:
                df.to_csv(csv_file, index=False)
                print(f"Exported {topic_name} to {csv_file} ({len(df)} records)")
            
        return self.csv_files

    def _messages_to_dataframe(self, topic_name: str, messages: List) -> Optional[pd.DataFrame]:
        """
        Convert ROS messages to pandas DataFrame
        
        Args:
            topic_name: Name of the topic
            messages: List of messages with timestamps
            
        Returns:
            DataFrame with message data
        """
        if not messages:
            return None
            
        data_rows = []
        
        for msg_data in messages:
            timestamp = msg_data['timestamp']
            msg = msg_data['message']
            
            # Convert nanoseconds to seconds
            time_sec = timestamp / 1e9
            dt = datetime.fromtimestamp(time_sec)
            
            row = {'timestamp_ns': timestamp, 'timestamp_sec': time_sec, 'datetime': dt}
            
            if topic_name == '/state_estimation':  # Odometry
                row.update({
                    'pos_x': msg.pose.pose.position.x,
                    'pos_y': msg.pose.pose.position.y,
                    'pos_z': msg.pose.pose.position.z,
                    'orient_x': msg.pose.pose.orientation.x,
                    'orient_y': msg.pose.pose.orientation.y,
                    'orient_z': msg.pose.pose.orientation.z,
                    'orient_w': msg.pose.pose.orientation.w,
                    'linear_vel_x': msg.twist.twist.linear.x,
                    'linear_vel_y': msg.twist.twist.linear.y,
                    'linear_vel_z': msg.twist.twist.linear.z,
                    'angular_vel_x': msg.twist.twist.angular.x,
                    'angular_vel_y': msg.twist.twist.angular.y,
                    'angular_vel_z': msg.twist.twist.angular.z,
                    'yaw': self._quaternion_to_yaw(msg.pose.pose.orientation)
                })
                
            elif topic_name == '/way_point':  # PointStamped
                row.update({
                    'target_x': msg.point.x,
                    'target_y': msg.point.y,
                    'target_z': msg.point.z
                })
                
            elif topic_name == '/cmd_vel':  # Twist
                row.update({
                    'cmd_linear_x': msg.linear.x,
                    'cmd_linear_y': msg.linear.y,
                    'cmd_linear_z': msg.linear.z,
                    'cmd_angular_x': msg.angular.x,
                    'cmd_angular_y': msg.angular.y,
                    'cmd_angular_z': msg.angular.z
                })
                
            data_rows.append(row)
        
        return pd.DataFrame(data_rows)

    def _quaternion_to_yaw(self, quat) -> float:
        """Convert quaternion to yaw angle"""
        siny_cosp = 2 * (quat.w * quat.z + quat.x * quat.y)
        cosy_cosp = 1 - 2 * (quat.y * quat.y + quat.z * quat.z)
        return math.atan2(siny_cosp, cosy_cosp)

    def analyze_trajectory_error(self, plot_results: bool = True) -> Dict:
        """
        Analyze trajectory tracking error for PID tuning
        
        Args:
            plot_results: Whether to generate plots
            
        Returns:
            Dictionary containing error analysis results
        """
        print("Analyzing trajectory tracking error...")
        
        # Load CSV data
        odom_file = self.csv_files.get('/state_estimation')
        waypoint_file = self.csv_files.get('/way_point')
        cmd_file = self.csv_files.get('/cmd_vel')
        
        if not odom_file or not waypoint_file:
            print("Error: Missing required CSV files for trajectory analysis")
            return {}
        
        # Load data
        odom_df = pd.read_csv(odom_file)
        waypoint_df = pd.read_csv(waypoint_file)
        cmd_df = pd.read_csv(cmd_file) if cmd_file else None
        
        # Convert datetime columns
        odom_df['datetime'] = pd.to_datetime(odom_df['datetime'])
        waypoint_df['datetime'] = pd.to_datetime(waypoint_df['datetime'])
        if cmd_df is not None:
            cmd_df['datetime'] = pd.to_datetime(cmd_df['datetime'])
        
        # Interpolate waypoints to match odometry timestamps
        waypoint_interp = self._interpolate_waypoints(odom_df, waypoint_df)
        
        # Calculate trajectory errors
        errors = self._calculate_trajectory_errors(odom_df, waypoint_interp)
        
        # Calculate performance metrics
        metrics = self._calculate_performance_metrics(errors)
        
        # Generate plots if requested
        if plot_results:
            self._plot_trajectory_analysis(odom_df, waypoint_interp, errors, cmd_df, metrics)
        
        # Save analysis results
        results = {
            'errors': errors,
            'metrics': metrics,
            'odom_data': odom_df,
            'waypoint_data': waypoint_interp,
            'cmd_data': cmd_df
        }
        
        # Save error data to CSV
        error_file = self.output_dir / "trajectory_errors.csv"
        errors.to_csv(error_file, index=False)
        print(f"Saved trajectory errors to {error_file}")
        
        # Save metrics to file
        metrics_file = self.output_dir / "performance_metrics.txt"
        with open(metrics_file, 'w') as f:
            f.write("PID Control Performance Metrics\n")
            f.write("=" * 40 + "\n\n")
            for key, value in metrics.items():
                f.write(f"{key}: {value}\n")
        print(f"Saved performance metrics to {metrics_file}")
        
        return results

    def _interpolate_waypoints(self, odom_df: pd.DataFrame, waypoint_df: pd.DataFrame) -> pd.DataFrame:
        """Interpolate waypoint data to match odometry timestamps"""
        if waypoint_df.empty:
            return pd.DataFrame()
        
        # Use forward fill for waypoints (step function)
        waypoint_resampled = waypoint_df.set_index('datetime').reindex(
            odom_df['datetime'], method='ffill'
        ).reset_index()
        
        return waypoint_resampled

    def _calculate_trajectory_errors(self, odom_df: pd.DataFrame, waypoint_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate trajectory tracking errors"""
        errors = odom_df[['datetime', 'timestamp_sec']].copy()
        
        if not waypoint_df.empty:
            # Position errors
            errors['error_x'] = odom_df['pos_x'] - waypoint_df['target_x']
            errors['error_y'] = odom_df['pos_y'] - waypoint_df['target_y']
            errors['error_distance'] = np.sqrt(errors['error_x']**2 + errors['error_y']**2)
            
            # Add actual and target positions for plotting
            errors['actual_x'] = odom_df['pos_x']
            errors['actual_y'] = odom_df['pos_y']
            errors['target_x'] = waypoint_df['target_x']
            errors['target_y'] = waypoint_df['target_y']
        else:
            # No waypoint data available
            errors['error_x'] = 0
            errors['error_y'] = 0
            errors['error_distance'] = 0
            errors['actual_x'] = odom_df['pos_x']
            errors['actual_y'] = odom_df['pos_y']
            errors['target_x'] = odom_df['pos_x']  # Use actual as fallback
            errors['target_y'] = odom_df['pos_y']
        
        # Velocity and orientation data
        errors['actual_yaw'] = odom_df['yaw']
        errors['linear_vel'] = odom_df['linear_vel_x']
        errors['angular_vel'] = odom_df['angular_vel_z']
        
        return errors

    def _calculate_performance_metrics(self, errors: pd.DataFrame) -> Dict:
        """Calculate PID performance metrics"""
        metrics = {}
        
        if 'error_distance' in errors.columns:
            # Tracking errors
            metrics['Mean Absolute Error (m)'] = f"{errors['error_distance'].mean():.4f}"
            metrics['RMS Error (m)'] = f"{np.sqrt((errors['error_distance']**2).mean()):.4f}"
            metrics['Max Error (m)'] = f"{errors['error_distance'].max():.4f}"
            metrics['Std Dev Error (m)'] = f"{errors['error_distance'].std():.4f}"
            
            # Settling analysis (when error < 5% of max error)
            max_error = errors['error_distance'].max()
            settling_threshold = 0.05 * max_error if max_error > 0 else 0.1
            settled_mask = errors['error_distance'] < settling_threshold
            if settled_mask.any():
                first_settled_idx = settled_mask.idxmax()
                settling_time = errors.iloc[first_settled_idx]['timestamp_sec'] - errors.iloc[0]['timestamp_sec']
                metrics['Settling Time (s)'] = f"{settling_time:.2f}"
            else:
                metrics['Settling Time (s)'] = "Not settled"
        
        # Velocity statistics
        if 'linear_vel' in errors.columns:
            metrics['Mean Linear Velocity (m/s)'] = f"{errors['linear_vel'].mean():.4f}"
            metrics['Max Linear Velocity (m/s)'] = f"{errors['linear_vel'].max():.4f}"
            
        if 'angular_vel' in errors.columns:
            metrics['Mean Angular Velocity (rad/s)'] = f"{errors['angular_vel'].mean():.4f}"
            metrics['Max Angular Velocity (rad/s)'] = f"{errors['angular_vel'].max():.4f}"
        
        return metrics

    def _plot_trajectory_analysis(self, odom_df: pd.DataFrame, waypoint_df: pd.DataFrame, 
                                 errors: pd.DataFrame, cmd_df: Optional[pd.DataFrame], 
                                 metrics: Dict):
        """Generate comprehensive plots for PID tuning analysis"""
        print("Generating analysis plots...")
        
        # Create figure with subplots
        fig = plt.figure(figsize=(16, 12))
        
        # 1. Trajectory Plot (X-Y)
        ax1 = plt.subplot(2, 3, 1)
        ax1.plot(errors['actual_x'], errors['actual_y'], 'b-', label='Actual Path', linewidth=2)
        ax1.plot(errors['target_x'], errors['target_y'], 'r--', label='Target Path', linewidth=2)
        ax1.scatter(errors['actual_x'].iloc[0], errors['actual_y'].iloc[0], c='green', s=100, label='Start', zorder=5)
        ax1.scatter(errors['actual_x'].iloc[-1], errors['actual_y'].iloc[-1], c='red', s=100, label='End', zorder=5)
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')
        ax1.set_title('Trajectory Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')
        
        # 2. Position Error vs Time
        ax2 = plt.subplot(2, 3, 2)
        ax2.plot(errors['timestamp_sec'], errors['error_distance'], 'r-', linewidth=2)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Position Error (m)')
        ax2.set_title('Position Tracking Error')
        ax2.grid(True, alpha=0.3)
        
        # 3. X-Y Error Components
        ax3 = plt.subplot(2, 3, 3)
        ax3.plot(errors['timestamp_sec'], errors['error_x'], 'r-', label='X Error', linewidth=2)
        ax3.plot(errors['timestamp_sec'], errors['error_y'], 'b-', label='Y Error', linewidth=2)
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Position Error (m)')
        ax3.set_title('X-Y Error Components')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Vehicle Velocities
        ax4 = plt.subplot(2, 3, 4)
        ax4.plot(errors['timestamp_sec'], errors['linear_vel'], 'g-', label='Linear Vel', linewidth=2)
        ax4_twin = ax4.twinx()
        ax4_twin.plot(errors['timestamp_sec'], errors['angular_vel'], 'orange', label='Angular Vel', linewidth=2)
        ax4.set_xlabel('Time (s)')
        ax4.set_ylabel('Linear Velocity (m/s)', color='g')
        ax4_twin.set_ylabel('Angular Velocity (rad/s)', color='orange')
        ax4.set_title('Vehicle Velocities')
        ax4.grid(True, alpha=0.3)
        
        # 5. Control Commands (if available)
        ax5 = plt.subplot(2, 3, 5)
        if cmd_df is not None and not cmd_df.empty:
            ax5.plot(cmd_df['timestamp_sec'], cmd_df['cmd_linear_x'], 'purple', label='Linear Cmd', linewidth=2)
            ax5_twin = ax5.twinx()
            ax5_twin.plot(cmd_df['timestamp_sec'], cmd_df['cmd_angular_z'], 'brown', label='Angular Cmd', linewidth=2)
            ax5.set_ylabel('Linear Command (m/s)', color='purple')
            ax5_twin.set_ylabel('Angular Command (rad/s)', color='brown')
        else:
            ax5.text(0.5, 0.5, 'No Control Command Data', ha='center', va='center', transform=ax5.transAxes)
        ax5.set_xlabel('Time (s)')
        ax5.set_title('Control Commands')
        ax5.grid(True, alpha=0.3)
        
        # 6. Performance Metrics Text
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        metrics_text = "Performance Metrics:\n\n"
        for key, value in metrics.items():
            metrics_text += f"{key}: {value}\n"
        ax6.text(0.1, 0.9, metrics_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        
        # Save plot
        plot_file = self.output_dir / "trajectory_analysis.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Saved trajectory analysis plot to {plot_file}")
        
        # Show plot if in interactive mode
        try:
            plt.show()
        except:
            pass  # Non-interactive environment
        
        plt.close()

    def generate_report(self):
        """Generate a comprehensive analysis report"""
        report_file = self.output_dir / "analysis_report.md"
        
        with open(report_file, 'w') as f:
            f.write("# ROS2 Bag Analysis Report\n\n")
            f.write(f"**Bag File:** `{self.bag_path}`\n")
            f.write(f"**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Extracted Topics\n\n")
            for topic, csv_file in self.csv_files.items():
                f.write(f"- `{topic}` → `{Path(csv_file).name}`\n")
            
            f.write("\n## Files Generated\n\n")
            f.write("- `trajectory_errors.csv` - Detailed trajectory tracking errors\n")
            f.write("- `performance_metrics.txt` - PID performance metrics\n")
            f.write("- `trajectory_analysis.png` - Comprehensive analysis plots\n")
            f.write("- `analysis_report.md` - This report\n\n")
            
            f.write("## PID Tuning Recommendations\n\n")
            f.write("Based on the trajectory analysis:\n\n")
            f.write("1. **Large steady-state error**: Increase Kp (proportional gain)\n")
            f.write("2. **Oscillations**: Decrease Kp or increase Kd (derivative gain)\n")
            f.write("3. **Slow response**: Increase Kp or add Ki (integral gain)\n")
            f.write("4. **Overshoot**: Increase Kd or decrease Kp\n")
            f.write("5. **Persistent offset**: Increase Ki\n\n")
            
            f.write("## Usage\n\n")
            f.write("```bash\n")
            f.write("# Analyze a bag file\n")
            f.write("python ros2bag_analyzer.py /path/to/bagfile\n\n")
            f.write("# Specify output directory\n")
            f.write("python ros2bag_analyzer.py /path/to/bagfile --output-dir ./analysis\n")
            f.write("```\n")
        
        print(f"Generated analysis report: {report_file}")


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(description='Analyze ROS2 bag files for PID tuning')
    parser.add_argument('bag_path', help='Path to ROS2 bag file')
    parser.add_argument('--output-dir', default='analysis_output', 
                       help='Output directory for CSV files and plots')
    parser.add_argument('--no-plots', action='store_true',
                       help='Skip generating plots')
    
    args = parser.parse_args()
    
    # Validate bag path
    if not os.path.exists(args.bag_path):
        print(f"Error: Bag file not found: {args.bag_path}")
        sys.exit(1)
    
    try:
        # Initialize analyzer
        analyzer = ROS2BagAnalyzer(args.bag_path, args.output_dir)
        
        # Read bag file
        analyzer.read_bag_file()
        
        # Export to CSV
        analyzer.export_to_csv()
        
        # Analyze trajectory errors
        analyzer.analyze_trajectory_error(plot_results=not args.no_plots)
        
        # Generate report
        analyzer.generate_report()
        
        print(f"\nAnalysis complete! Results saved to: {analyzer.output_dir}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()