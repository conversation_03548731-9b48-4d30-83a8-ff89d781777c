#!/usr/bin/env python3
"""
Test script to verify gentle recovery control after odometry loss
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
import time
import math

class RecoveryControlTest(Node):
    """Test node to verify gentle recovery control"""
    
    def __init__(self):
        super().__init__('recovery_control_test')
        
        # Publishers
        self.odom_pub = self.create_publisher(Odometry, '/state_estimation', 10)
        
        # Subscribers
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10
        )
        
        # Test state
        self.test_phase = 0
        self.cmd_vel_history = []
        self.test_start_time = time.time()
        
        # Test phases:
        # 0: Normal operation (0-5s)
        # 1: Odometry loss (5-8s)
        # 2: Recovery with misalignment (8-15s) - simulate large angle error
        # 3: Verify gentle control (15-20s)
        # 4: Test complete (20s+)
        
        self.test_timer = self.create_timer(0.1, self.run_test)
        self.get_logger().info('Recovery control test started')
    
    def cmd_vel_callback(self, msg):
        """Monitor cmd_vel commands during recovery"""
        current_time = time.time() - self.test_start_time
        
        # Record all commands with timestamps
        cmd_data = {
            'time': current_time,
            'phase': self.test_phase,
            'linear_x': msg.linear.x,
            'angular_z': msg.angular.z,
            'is_aggressive': abs(msg.angular.z) > 0.8  # Consider > 0.8 rad/s as aggressive
        }
        
        self.cmd_vel_history.append(cmd_data)
        
        # Log aggressive commands during recovery
        if self.test_phase == 2 and cmd_data['is_aggressive']:
            self.get_logger().warn(
                f'Aggressive angular command during recovery at t={current_time:.1f}s: '
                f'angular={msg.angular.z:.3f} rad/s'
            )
    
    def run_test(self):
        """Execute test phases"""
        current_time = time.time() - self.test_start_time
        
        if current_time < 5.0:
            # Phase 0: Normal operation
            if self.test_phase != 0:
                self.test_phase = 0
                self.get_logger().info('Phase 0: Normal operation')
            self.publish_normal_odometry()
            
        elif current_time < 8.0:
            # Phase 1: Odometry loss
            if self.test_phase != 1:
                self.test_phase = 1
                self.get_logger().warn('Phase 1: Simulating odometry loss')
            # Don't publish odometry
            
        elif current_time < 15.0:
            # Phase 2: Recovery with misalignment
            if self.test_phase != 2:
                self.test_phase = 2
                self.get_logger().info('Phase 2: Recovery with large angle error')
            self.publish_misaligned_odometry()
            
        elif current_time < 20.0:
            # Phase 3: Verify gentle control continues
            if self.test_phase != 3:
                self.test_phase = 3
                self.get_logger().info('Phase 3: Verifying continued gentle control')
            self.publish_aligned_odometry()
            
        else:
            # Phase 4: Test complete
            if self.test_phase != 4:
                self.test_phase = 4
                self.analyze_results()
                self.test_timer.cancel()
    
    def publish_normal_odometry(self):
        """Publish normal odometry data"""
        odom_msg = self.create_odometry_msg(0.0, 0.0, 0.0)  # x, y, yaw
        self.odom_pub.publish(odom_msg)
    
    def publish_misaligned_odometry(self):
        """Publish odometry with large angle error (90 degrees off)"""
        # Position close to waypoint but facing wrong direction
        odom_msg = self.create_odometry_msg(0.5, 0.5, math.pi/2)  # 90 degrees off
        self.odom_pub.publish(odom_msg)
    
    def publish_aligned_odometry(self):
        """Publish gradually aligning odometry"""
        current_time = time.time() - self.test_start_time
        # Gradually align from 90 degrees to 0 degrees
        progress = min(1.0, (current_time - 15.0) / 5.0)
        yaw = math.pi/2 * (1.0 - progress)
        odom_msg = self.create_odometry_msg(0.5, 0.5, yaw)
        self.odom_pub.publish(odom_msg)
    
    def create_odometry_msg(self, x, y, yaw):
        """Create odometry message with given position and orientation"""
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'map'
        odom_msg.child_frame_id = 'base_link'
        
        # Position
        odom_msg.pose.pose.position.x = x
        odom_msg.pose.pose.position.y = y
        odom_msg.pose.pose.position.z = 0.0
        
        # Orientation from yaw
        odom_msg.pose.pose.orientation.w = math.cos(yaw / 2)
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = math.sin(yaw / 2)
        
        return odom_msg
    
    def analyze_results(self):
        """Analyze test results"""
        self.get_logger().info('=== RECOVERY CONTROL TEST RESULTS ===')
        
        # Analyze each phase
        phase_data = {}
        for phase in range(4):
            phase_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == phase]
            aggressive_commands = [cmd for cmd in phase_commands if cmd['is_aggressive']]
            
            if phase_commands:
                max_angular = max([abs(cmd['angular_z']) for cmd in phase_commands])
                avg_angular = sum([abs(cmd['angular_z']) for cmd in phase_commands]) / len(phase_commands)
            else:
                max_angular = 0.0
                avg_angular = 0.0
            
            phase_data[phase] = {
                'total_commands': len(phase_commands),
                'aggressive_commands': len(aggressive_commands),
                'max_angular': max_angular,
                'avg_angular': avg_angular
            }
        
        # Phase 0: Baseline
        self.get_logger().info(f'Phase 0 (Baseline): max angular = {phase_data[0]["max_angular"]:.3f} rad/s')
        
        # Phase 1: Should stop during odometry loss
        if phase_data[1]['max_angular'] < 0.1:
            self.get_logger().info('✓ Phase 1: Vehicle stopped during odometry loss')
        else:
            self.get_logger().warn(f'⚠ Phase 1: Vehicle not fully stopped (max: {phase_data[1]["max_angular"]:.3f})')
        
        # Phase 2: Recovery should be gentle
        recovery_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == 2]
        early_recovery = [cmd for cmd in recovery_commands if 8.0 <= cmd['time'] <= 11.0]  # First 3 seconds
        
        if early_recovery:
            early_max_angular = max([abs(cmd['angular_z']) for cmd in early_recovery])
            early_avg_angular = sum([abs(cmd['angular_z']) for cmd in early_recovery]) / len(early_recovery)
            
            if early_max_angular <= 0.6:  # Should be limited during recovery
                self.get_logger().info(f'✓ Phase 2: Gentle recovery control (max: {early_max_angular:.3f} rad/s)')
            else:
                self.get_logger().warn(f'⚠ Phase 2: Recovery control too aggressive (max: {early_max_angular:.3f} rad/s)')
            
            self.get_logger().info(f'Early recovery stats: max={early_max_angular:.3f}, avg={early_avg_angular:.3f} rad/s')
        
        # Phase 3: Should continue gentle control
        if phase_data[3]['aggressive_commands'] == 0:
            self.get_logger().info('✓ Phase 3: No aggressive commands in final phase')
        else:
            self.get_logger().warn(f'⚠ Phase 3: {phase_data[3]["aggressive_commands"]} aggressive commands')
        
        # Overall assessment
        recovery_gentle = (
            phase_data[2]['aggressive_commands'] <= 2 and  # Allow some aggressive commands
            early_max_angular <= 0.8 if early_recovery else True  # Early recovery should be gentle
        )
        
        if recovery_gentle:
            self.get_logger().info('🎉 OVERALL: Recovery control test PASSED - gentle control achieved')
        else:
            self.get_logger().error('❌ OVERALL: Recovery control test FAILED - control too aggressive')
        
        # Print detailed summary
        self.get_logger().info(f'Total commands recorded: {len(self.cmd_vel_history)}')
        for phase in range(4):
            self.get_logger().info(
                f'Phase {phase}: {phase_data[phase]["aggressive_commands"]}/{phase_data[phase]["total_commands"]} aggressive, '
                f'max: {phase_data[phase]["max_angular"]:.3f}, avg: {phase_data[phase]["avg_angular"]:.3f} rad/s'
            )
        
        # Recommendations
        if phase_data[2]['max_angular'] > 1.0:
            self.get_logger().info('💡 RECOMMENDATION: Consider reducing recovery_min_authority or increasing recovery_duration')
        elif phase_data[2]['max_angular'] < 0.3:
            self.get_logger().info('💡 RECOMMENDATION: Recovery might be too conservative, consider increasing recovery_min_authority')


def main(args=None):
    rclpy.init(args=args)
    
    test_node = RecoveryControlTest()
    
    try:
        rclpy.spin(test_node)
    except KeyboardInterrupt:
        pass
    finally:
        test_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
