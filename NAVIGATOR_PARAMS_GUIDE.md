# Differential Tracked Navigator 参数配置指南

本文档详细说明了 `navigator_params.yaml` 配置文件中各个参数的作用、使用情况和调优建议。

## 📋 目录

- [车辆物理参数](#车辆物理参数)
- [控制参数](#控制参数)
- [PID控制参数](#pid控制参数)
- [PID安全参数](#pid安全参数)
- [安全参数](#安全参数)
- [里程计健康监控参数](#里程计健康监控参数)
- [导航恢复参数](#导航恢复参数)
- [位置跳跃检测参数](#位置跳跃检测参数)
- [恢复控制参数](#恢复控制参数)
- [Waypoint过渡参数](#waypoint过渡参数)
- [文件路径](#文件路径)

---

## 🚗 车辆物理参数

### `vehicle.wheelbase: 1.5`
- **作用**: 前后轴之间的距离（米）
- **使用状态**: ✅ **已使用** - 在车辆运动学计算中使用
- **调优建议**: 
  - 设置为实际车辆的轴距
  - 影响转弯半径计算
  - 典型值：0.5-3.0米

### `vehicle.track_width: 1.69`
- **作用**: 左右履带之间的距离（米）
- **使用状态**: ✅ **已使用** - 用于障碍物检测的安全区域计算
- **调优建议**:
  - 设置为实际车辆的履带宽度
  - 影响障碍物检测范围
  - 典型值：0.4-2.0米

### `vehicle.max_linear_velocity: 1.5`
- **作用**: 最大线性速度（米/秒）
- **使用状态**: ✅ **已使用** - 控制输出限制
- **调优建议**:
  - 根据车辆性能和安全要求设置
  - 过高可能导致控制不稳定
  - 典型值：0.5-3.0 m/s

### `vehicle.max_angular_velocity: 1.2`
- **作用**: 最大角速度（弧度/秒）
- **使用状态**: ✅ **已使用** - 控制输出限制
- **调优建议**:
  - 根据车辆转向能力设置
  - 过高可能导致急转弯
  - 典型值：0.5-2.0 rad/s

### `vehicle.min_linear_velocity: 0.1`
- **作用**: 最小线性速度（米/秒）
- **使用状态**: ✅ **已使用** - 防止车辆过慢移动
- **调优建议**:
  - 避免设置过高，可能导致无法精确停车
  - 典型值：0.05-0.2 m/s

### `vehicle.wheel_radius: 1.5`
- **作用**: 车轮半径（米）
- **使用状态**: ✅ **已使用** - 运动学计算
- **调优建议**:
  - 设置为实际车轮半径
  - 影响速度和位置计算精度

---

## 🎮 控制参数

### `control.lookahead_distance: 2.0`
- **作用**: Pure Pursuit前瞻距离（米）
- **使用状态**: ❌ **未使用** - 参数已定义但在当前PID控制算法中未使用
- **说明**: 系统使用PID控制而非Pure Pursuit算法
- **建议**: 可以移除或用于未来的Pure Pursuit实现

### `control.goal_tolerance: 0.2`
- **作用**: 到达waypoint的距离容差（米）
- **使用状态**: ✅ **已使用** - 判断是否到达当前waypoint
- **关键功能**:
  - 触发waypoint切换
  - 重置PID控制器
  - 计算waypoint跳跃阈值
- **调优建议**:
  - 较小值(0.1m)：更精确但可能卡住
  - 较大值(0.5m)：更流畅但精度降低
  - 推荐：车辆长度的0.5-1倍

### `control.angular_tolerance: 0.15`
- **作用**: 角度容差（弧度）
- **使用状态**: ❌ **未使用** - 系统使用动态角度容差策略
- **说明**: 实际使用45度基础容差+距离自适应调整
- **建议**: 可以用作基础角度容差替换硬编码值

### `control.control_frequency: 10.0`
- **作用**: 控制循环频率（赫兹）
- **使用状态**: ✅ **已使用** - 控制循环定时器和PID计算
- **调优建议**:
  - 较高频率：更平滑控制，但计算负载大
  - 较低频率：计算轻松，但可能不稳定
  - 推荐：5-20 Hz

### `control.fast_turn_mode: true`
- **作用**: 启用快速转弯模式
- **使用状态**: ✅ **已使用** - 大角度误差时增强角速度响应
- **效果**: 角度误差>30度时，角速度增益提升至1.5倍
- **调优建议**: 
  - true：快速转弯，适合开阔环境
  - false：平稳转弯，适合狭窄空间

---

## 🔧 PID控制参数

### 线性控制PID
```yaml
control.linear_kp: 2.0    # 比例增益
control.linear_ki: 0.1    # 积分增益  
control.linear_kd: 0.05   # 微分增益
```
- **使用状态**: ✅ **已使用** - 控制车辆前进速度
- **调优指南**:
  - **Kp过大**: 振荡、超调
  - **Kp过小**: 响应慢、稳态误差大
  - **Ki**: 消除稳态误差，但可能引起振荡
  - **Kd**: 减少超调，提高稳定性

### 角度控制PID
```yaml
control.angular_kp: 1.5   # 比例增益
control.angular_ki: 0.08  # 积分增益
control.angular_kd: 0.08  # 微分增益
```
- **使用状态**: ✅ **已使用** - 控制车辆转向
- **调优指南**:
  - 角度控制通常需要更保守的参数
  - 过大的Ki可能导致转向振荡
  - Kd有助于减少转向超调

---

## 🛡️ PID安全参数

### `control.max_integral_windup: 5.0`
- **作用**: 最大积分项限制，防止积分饱和
- **使用状态**: ✅ **已使用** - 应用于线性和角度PID控制器
- **重要性**: 防止积分项过度累积导致的控制不稳定
- **调优建议**:
  - 经验公式：max_integral ≈ (期望最大输出) / ki
  - 典型值：3.0-10.0
  - 观察是否经常达到限制值

### `control.enable_integral_reset: true`
- **作用**: 启用积分项重置
- **使用状态**: ✅ **已使用** - 在waypoint切换和里程计丢失时重置
- **建议**: 保持启用以提高控制稳定性

---

## 🚨 安全参数

### `safety.obstacle_detection_range: 2.0`
- **作用**: 障碍物检测范围（米）
- **使用状态**: ✅ **已使用** - LiDAR障碍物检测
- **调优建议**: 根据车辆速度和制动距离设置

### `safety.safety_stop_distance: 0.8`
- **作用**: 安全停车距离（米）
- **使用状态**: ✅ **已使用** - 检测到障碍物时的停车距离
- **调优建议**: 应大于紧急停车距离

### `safety.emergency_stop_distance: 0.3`
- **作用**: 紧急停车距离（米）
- **使用状态**: ✅ **已使用** - 立即停车的最小距离
- **调优建议**: 设置为车辆长度的一半左右

### `safety.enable_obstacle_detection: false`
- **作用**: 启用/禁用障碍物检测
- **使用状态**: ✅ **已使用** - 控制是否进行LiDAR障碍物检测
- **默认**: 禁用（适合仿真环境）

---

## 📡 里程计健康监控参数

### `odometry.enable_health_check: false`
- **作用**: 启用里程计健康检查
- **使用状态**: ✅ **已使用** - 监控里程计数据质量
- **默认**: 禁用（避免仿真中的问题）

### `odometry.timeout: 3.0`
- **作用**: 里程计超时时间（秒）
- **使用状态**: ✅ **已使用** - 检测里程计数据丢失
- **调优建议**: 根据系统延迟调整

### 其他里程计参数
```yaml
odometry.startup_grace_period: 10.0       # 启动宽限期
odometry.max_consecutive_bad_readings: 5  # 最大连续坏读数
odometry.max_position_jump: 2.0           # 最大位置跳跃
odometry.max_velocity_bound: 5.0          # 最大合理速度
odometry.max_angular_velocity_bound: 5.0  # 最大合理角速度
```
- **使用状态**: ✅ **已使用** - 里程计数据验证

---

## 🔄 导航恢复参数

### `navigation.auto_resume_after_odom_recovery: true`
- **作用**: 里程计恢复后自动恢复导航
- **使用状态**: ✅ **已使用** - 自动恢复导航功能

### `navigation.recalculate_waypoint_on_recovery: true`
- **作用**: 恢复时重新计算最近waypoint
- **使用状态**: ✅ **已使用** - 智能waypoint选择

### `navigation.max_waypoint_skip_distance: 1.0`
- **作用**: 最大waypoint跳跃距离（米）
- **使用状态**: ✅ **已使用** - 计算waypoint跳跃阈值
- **公式**: `skip_threshold = min(max_waypoint_skip_distance, goal_tolerance * 3)`

---

## 📍 位置跳跃检测参数

### `navigation.enable_position_jump_detection: false`
- **作用**: 启用位置跳跃检测
- **使用状态**: ✅ **已使用** - 检测异常的位置变化
- **默认**: 禁用（避免仿真中的问题）

### 相关参数
```yaml
navigation.position_jump_threshold: 2.0      # 位置跳跃阈值
navigation.required_stable_readings: 5       # 需要的稳定读数
navigation.max_reasonable_distance: 50.0     # 最大合理距离
navigation.emergency_angular_limit: 0.5      # 紧急角速度限制
navigation.emergency_linear_limit: 0.5       # 紧急线速度限制
```
- **使用状态**: ✅ **已使用** - 位置异常检测和安全限制

---

## 🛠️ 恢复控制参数

### `navigation.recovery_duration: 3.0`
- **作用**: 恢复后的温和控制持续时间（秒）
- **使用状态**: ✅ **已使用** - 恢复模式控制因子计算

### `navigation.recovery_min_authority: 0.3`
- **作用**: 恢复期间的最小控制权限（0.0-1.0）
- **使用状态**: ✅ **已使用** - 限制恢复期间的控制强度

### `navigation.recovery_angle_tolerance: 45.0`
- **作用**: 恢复期间的角度容差（度）
- **使用状态**: ✅ **已使用** - 恢复模式下更宽松的角度要求

---

## 🎯 Waypoint过渡参数

### `navigation.waypoint_transition_duration: 2.0`
- **作用**: waypoint切换后的温和控制持续时间（秒）
- **使用状态**: ✅ **已使用** - waypoint过渡模式

### `navigation.waypoint_transition_min_authority: 0.4`
- **作用**: waypoint过渡期间的最小控制权限（0.0-1.0）
- **使用状态**: ✅ **已使用** - 平滑的waypoint切换

---

## 📁 文件路径

### `waypoints_file: "point.txt"`
- **作用**: waypoint文件路径
- **使用状态**: ✅ **已使用** - 加载导航路径点
- **格式**: 每行包含 `x y` 坐标（以空格分隔）

---

## 🎛️ 调优建议总结

### 🚀 性能优化
1. **提高响应速度**: 增大PID的Kp值
2. **减少振荡**: 增大Kd值，减小Ki值
3. **提高精度**: 减小goal_tolerance
4. **加快转弯**: 启用fast_turn_mode

### 🛡️ 稳定性优化
1. **防止积分饱和**: 适当设置max_integral_windup
2. **处理数据异常**: 启用相关健康检查
3. **平滑控制**: 使用恢复和过渡模式

### ⚡ 常见问题解决
- **车辆振荡**: 减小Kp，增大Kd
- **响应慢**: 增大Kp，检查control_frequency
- **无法到达waypoint**: 增大goal_tolerance
- **转弯过急**: 禁用fast_turn_mode，减小angular_kp

---

## 📊 参数使用状态统计

- ✅ **已使用参数**: 32个
- ❌ **未使用参数**: 2个 (lookahead_distance, angular_tolerance)
- 📊 **使用率**: 94%

大部分参数都在实际控制中发挥作用，系统设计较为完整和实用。

---

## 🔧 PID调优详细指南

### 线性PID调优步骤
1. **设置Ki=0, Kd=0**，只调Kp
2. **逐渐增大Kp**直到出现轻微振荡
3. **减小Kp**到振荡消失
4. **增加Kd**减少超调
5. **最后添加Ki**消除稳态误差

### 角度PID调优步骤
1. **从较小的Kp开始**（如1.0）
2. **观察转向响应**，逐渐增大
3. **添加Kd**提高稳定性
4. **谨慎使用Ki**，避免转向振荡

### 调优工具
- 使用ROS2 bag记录数据
- 分析轨迹跟踪误差
- 监控控制输出
- 观察实际车辆行为

---

## 🚨 安全注意事项

### 参数修改前的检查
1. **备份当前配置**
2. **在安全环境测试**
3. **逐步调整参数**
4. **监控系统行为**

### 危险参数组合
- **过大的max_velocity + 过大的Kp**: 可能导致失控
- **过小的goal_tolerance + 高精度要求**: 可能无法到达目标
- **禁用所有安全检查**: 在实际环境中危险

### 推荐的安全设置
```yaml
# 保守的安全设置
vehicle.max_linear_velocity: 1.0
vehicle.max_angular_velocity: 0.8
safety.enable_obstacle_detection: true
odometry.enable_health_check: true
navigation.enable_position_jump_detection: true
```

---

## 📈 性能监控指标

### 关键指标
1. **轨迹跟踪误差**: 实际路径与期望路径的偏差
2. **到达时间**: 完成导航任务的时间
3. **控制平滑度**: 速度和角速度的变化率
4. **稳定性**: 是否出现振荡或发散

### 监控方法
- ROS2 topic监控：`/cmd_vel`, `/state_estimation`
- 日志分析：控制器输出和误差
- 可视化：RViz轨迹显示
- 数据记录：bag文件分析

---

## 🔄 版本兼容性

### 当前版本特性
- PID控制算法
- 动态角度容差
- 积分饱和保护
- 多层安全检查
- 智能恢复机制

### 未来可能的改进
- Pure Pursuit算法支持
- 自适应PID参数
- 机器学习优化
- 多传感器融合

---

## 📚 参考资源

### 相关文档
- ROS2官方文档
- PID控制理论
- 移动机器人导航

### 调试工具
- RViz2可视化
- rqt_plot数据绘图
- ros2 bag数据记录
- ros2 param参数管理

### 社区支持
- ROS2 Discourse论坛
- GitHub Issues
- 技术文档Wiki

---

*本文档最后更新：2024年*
*版本：v1.0*
