differential_tracked_navigator:
  ros__parameters:
    # Vehicle physical parameters
    vehicle.wheelbase: 1.5                    # Distance between front and rear axles (meters)
    vehicle.track_width: 1.69                 # Distance between left and right tracks (meters)
    vehicle.max_linear_velocity: 1.5          # Maximum linear velocity (m/s)
    vehicle.max_angular_velocity: 1.2        # Maximum angular velocity (rad/s)
    vehicle.min_linear_velocity: 0.1          # Minimum linear velocity (m/s)
    vehicle.wheel_radius: 1.5                 # Wheel radius (meters)

    # Control parameters
    control.lookahead_distance: 2.0           # Pure pursuit lookahead distance (meters)
    control.goal_tolerance: 0.2               # Distance tolerance for reaching waypoints (meters)
    control.angular_tolerance: 0.15            # Angular tolerance for orientation (radians)
    control.control_frequency: 10.0           # Control loop frequency (Hz)
    control.fast_turn_mode: true              # Enable fast turning response
    
    # PID control parameters - Linear control
    control.linear_kp: 2.0                    # Linear PID proportional gain
    control.linear_ki: 0.1                    # Linear PID integral gain
    control.linear_kd: 0.05                   # Linear PID derivative gain
    
    # PID control parameters - Angular control
    control.angular_kp: 1.5                   # Angular PID proportional gain
    control.angular_ki: 0.08                   # Angular PID integral gain
    control.angular_kd: 0.08                   # Angular PID derivative gain

    # PID safety parameters
    control.max_integral_windup: 5.0          # Maximum integral term to prevent windup
    control.enable_integral_reset: true       # Reset integral on odometry loss

    # Safety parameters
    safety.obstacle_detection_range: 2.0      # Range for obstacle detection (meters)
    safety.safety_stop_distance: 0.8          # Distance to stop before obstacles (meters)
    safety.emergency_stop_distance: 0.3       # Emergency stop distance (meters)
    safety.enable_obstacle_detection: false   # Enable/disable obstacle detection (default: false)

    # Odometry health monitoring parameters (DISABLED)
    odometry.enable_health_check: false       # Enable/disable odometry health monitoring
    odometry.timeout: 3.0                     # Odometry timeout in seconds (increased from 2.0)
    odometry.startup_grace_period: 10.0       # Grace period after startup (seconds)
    odometry.max_consecutive_bad_readings: 5  # Max consecutive bad readings before stopping
    odometry.max_position_jump: 2.0           # Max allowed position jump per update (meters)
    odometry.max_velocity_bound: 5.0          # Max reasonable velocity (m/s)
    odometry.max_angular_velocity_bound: 5.0  # Max reasonable angular velocity (rad/s)

    # Navigation recovery parameters
    navigation.auto_resume_after_odom_recovery: true  # Auto resume navigation after odometry recovery
    navigation.recalculate_waypoint_on_recovery: true # Recalculate closest waypoint on recovery
    navigation.max_waypoint_skip_distance: 1.0       # Max distance to skip to closer waypoint (meters)
    navigation.smart_startup: true                   # Enable smart waypoint selection on startup
    navigation.startup_position_timeout: 5.0        # Wait time for position before starting navigation (seconds)
    navigation.startup_gentle_control: true          # Enable gentle control during startup phase

    # Position jump detection parameters (DISABLED)
    navigation.enable_position_jump_detection: false # Enable/disable position jump detection
    navigation.position_jump_threshold: 2.0          # Max allowed position change per update (meters)
    navigation.required_stable_readings: 5           # Required stable readings after position jump
    navigation.max_reasonable_distance: 50.0         # Max reasonable distance to waypoint (meters)
    navigation.emergency_angular_limit: 0.5          # Emergency angular velocity limit (rad/s)
    navigation.emergency_linear_limit: 0.5           # Emergency linear velocity limit (m/s)

    # Recovery control parameters
    navigation.recovery_duration: 3.0                # Duration of gentle control after recovery (seconds)
    navigation.recovery_min_authority: 0.3           # Minimum control authority during recovery (0.0-1.0)
    navigation.recovery_angle_tolerance: 45.0        # Angle tolerance during recovery (degrees)

    # Waypoint transition parameters
    navigation.waypoint_transition_duration: 2.0     # Duration of gentle control after waypoint switch (seconds)
    navigation.waypoint_transition_min_authority: 0.4 # Minimum control authority during waypoint transition (0.0-1.0)

    # File paths
    waypoints_file: "point.txt"               # Path to waypoints file