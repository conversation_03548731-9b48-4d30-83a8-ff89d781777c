#!/usr/bin/env python3
"""
Test script to verify position jump protection functionality
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
import time
import math

class PositionJumpTest(Node):
    """Test node to verify position jump protection"""
    
    def __init__(self):
        super().__init__('position_jump_test')
        
        # Publishers
        self.odom_pub = self.create_publisher(Odometry, '/state_estimation', 10)
        
        # Subscribers
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10
        )
        
        # Test state
        self.test_phase = 0
        self.cmd_vel_history = []
        self.test_start_time = time.time()
        self.current_position = [0.0, 0.0]  # [x, y]
        
        # Test phases:
        # 0: Normal operation (0-5s)
        # 1: Position jump (5-7s)
        # 2: Position stabilization (7-12s)
        # 3: Verify recovery (12-15s)
        # 4: Test complete (15s+)
        
        self.test_timer = self.create_timer(0.1, self.run_test)
        self.get_logger().info('Position jump protection test started')
    
    def cmd_vel_callback(self, msg):
        """Monitor cmd_vel commands for dangerous outputs"""
        current_time = time.time() - self.test_start_time
        
        # Record all commands with timestamps
        cmd_data = {
            'time': current_time,
            'phase': self.test_phase,
            'linear_x': msg.linear.x,
            'angular_z': msg.angular.z,
            'is_dangerous': abs(msg.linear.x) > 1.0 or abs(msg.angular.z) > 1.0
        }
        
        self.cmd_vel_history.append(cmd_data)
        
        # Log dangerous commands immediately
        if cmd_data['is_dangerous']:
            self.get_logger().error(
                f'DANGEROUS COMMAND at t={current_time:.1f}s, phase={self.test_phase}: '
                f'linear={msg.linear.x:.3f}, angular={msg.angular.z:.3f}'
            )
    
    def run_test(self):
        """Execute test phases"""
        current_time = time.time() - self.test_start_time
        
        if current_time < 5.0:
            # Phase 0: Normal operation
            if self.test_phase != 0:
                self.test_phase = 0
                self.get_logger().info('Phase 0: Normal operation')
            self.publish_normal_odometry()
            
        elif current_time < 7.0:
            # Phase 1: Position jump
            if self.test_phase != 1:
                self.test_phase = 1
                self.get_logger().warn('Phase 1: Simulating large position jump')
            self.publish_jumped_odometry()
            
        elif current_time < 12.0:
            # Phase 2: Position stabilization
            if self.test_phase != 2:
                self.test_phase = 2
                self.get_logger().info('Phase 2: Position stabilization')
                # Reset to reasonable position
                self.current_position = [1.0, 1.0]
            self.publish_stable_odometry()
            
        elif current_time < 15.0:
            # Phase 3: Verify recovery
            if self.test_phase != 3:
                self.test_phase = 3
                self.get_logger().info('Phase 3: Verifying recovery')
            self.publish_stable_odometry()
            
        else:
            # Phase 4: Test complete
            if self.test_phase != 4:
                self.test_phase = 4
                self.analyze_results()
                self.test_timer.cancel()
    
    def publish_normal_odometry(self):
        """Publish normal odometry data"""
        odom_msg = self.create_odometry_msg(self.current_position[0], self.current_position[1])
        self.odom_pub.publish(odom_msg)
    
    def publish_jumped_odometry(self):
        """Publish odometry with large position jump"""
        # Create a large position jump (10 meters)
        jumped_position = [self.current_position[0] + 10.0, self.current_position[1] + 10.0]
        odom_msg = self.create_odometry_msg(jumped_position[0], jumped_position[1])
        self.odom_pub.publish(odom_msg)
    
    def publish_stable_odometry(self):
        """Publish stable odometry data"""
        odom_msg = self.create_odometry_msg(self.current_position[0], self.current_position[1])
        self.odom_pub.publish(odom_msg)
    
    def create_odometry_msg(self, x, y):
        """Create odometry message with given position"""
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'map'
        odom_msg.child_frame_id = 'base_link'
        
        # Position
        odom_msg.pose.pose.position.x = x
        odom_msg.pose.pose.position.y = y
        odom_msg.pose.pose.position.z = 0.0
        
        # Orientation (facing forward)
        odom_msg.pose.pose.orientation.w = 1.0
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = 0.0
        
        return odom_msg
    
    def analyze_results(self):
        """Analyze test results"""
        self.get_logger().info('=== POSITION JUMP PROTECTION TEST RESULTS ===')
        
        # Analyze each phase
        phase_data = {}
        for phase in range(4):
            phase_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == phase]
            dangerous_commands = [cmd for cmd in phase_commands if cmd['is_dangerous']]
            
            phase_data[phase] = {
                'total_commands': len(phase_commands),
                'dangerous_commands': len(dangerous_commands),
                'max_linear': max([abs(cmd['linear_x']) for cmd in phase_commands], default=0.0),
                'max_angular': max([abs(cmd['angular_z']) for cmd in phase_commands], default=0.0)
            }
        
        # Phase 0: Should have normal operation
        self.get_logger().info(f'Phase 0 (Normal): {phase_data[0]["dangerous_commands"]} dangerous commands')
        
        # Phase 1: Should NOT have dangerous commands during position jump
        if phase_data[1]['dangerous_commands'] == 0:
            self.get_logger().info('✓ Phase 1: No dangerous commands during position jump')
        else:
            self.get_logger().error(f'❌ Phase 1: {phase_data[1]["dangerous_commands"]} dangerous commands during position jump!')
        
        # Phase 2: Should stabilize without dangerous commands
        if phase_data[2]['dangerous_commands'] == 0:
            self.get_logger().info('✓ Phase 2: No dangerous commands during stabilization')
        else:
            self.get_logger().warn(f'⚠ Phase 2: {phase_data[2]["dangerous_commands"]} dangerous commands during stabilization')
        
        # Phase 3: Should resume normal operation
        recovery_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == 3]
        active_recovery = [cmd for cmd in recovery_commands if abs(cmd['linear_x']) > 0.01 or abs(cmd['angular_z']) > 0.01]
        
        if len(active_recovery) > 0:
            self.get_logger().info('✓ Phase 3: Navigation resumed after stabilization')
        else:
            self.get_logger().warn('⚠ Phase 3: Navigation may not have resumed')
        
        # Overall assessment
        protection_successful = (
            phase_data[1]['dangerous_commands'] == 0 and  # No dangerous commands during jump
            phase_data[2]['dangerous_commands'] == 0      # No dangerous commands during stabilization
        )
        
        if protection_successful:
            self.get_logger().info('🎉 OVERALL: Position jump protection test PASSED')
        else:
            self.get_logger().error('❌ OVERALL: Position jump protection test FAILED')
        
        # Print detailed summary
        self.get_logger().info(f'Total commands recorded: {len(self.cmd_vel_history)}')
        for phase in range(4):
            self.get_logger().info(
                f'Phase {phase}: {phase_data[phase]["dangerous_commands"]}/{phase_data[phase]["total_commands"]} dangerous, '
                f'max linear: {phase_data[phase]["max_linear"]:.3f}, max angular: {phase_data[phase]["max_angular"]:.3f}'
            )
        
        # Check maximum velocities across all phases
        all_linear = [abs(cmd['linear_x']) for cmd in self.cmd_vel_history]
        all_angular = [abs(cmd['angular_z']) for cmd in self.cmd_vel_history]
        
        max_linear_overall = max(all_linear, default=0.0)
        max_angular_overall = max(all_angular, default=0.0)
        
        self.get_logger().info(f'Maximum velocities: linear={max_linear_overall:.3f} m/s, angular={max_angular_overall:.3f} rad/s')
        
        if max_linear_overall > 1.5 or max_angular_overall > 1.5:
            self.get_logger().warn('⚠ High maximum velocities detected - review safety limits')
        else:
            self.get_logger().info('✓ Maximum velocities within safe bounds')


def main(args=None):
    rclpy.init(args=args)
    
    test_node = PositionJumpTest()
    
    try:
        rclpy.spin(test_node)
    except KeyboardInterrupt:
        pass
    finally:
        test_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
