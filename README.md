# Differential Tracked Navigator

A ROS2-based navigation control system for differential tracked vehicles with support for waypoint navigation, obstacle avoidance, and map-based localization.

## Features

- **Configurable Vehicle Parameters**: Wheelbase, track width, maximum velocities
- **Map Integration**: Loads PGM maps using ROS2's map_server
- **Waypoint Navigation**: Reads waypoints from `point.txt` file
- **Pure Pursuit Control**: Implements pure pursuit algorithm for path following
- **Obstacle Detection**: Uses LiDAR data for real-time obstacle avoidance
- **ROS2 Bag Support**: Compatible with offline simulation using bag files
- **Visualization**: RViz integration for real-time monitoring

## Prerequisites

- ROS2 Humble or newer
- Python 3.8+
- Required ROS2 packages:
  - `nav2_map_server`
  - `nav2_lifecycle_manager`
  - `geometry_msgs`
  - `nav_msgs`
  - `sensor_msgs`
  - `rviz2`

## Installation

1. Clone this repository into your ROS2 workspace:
```bash
cd ~/ros2_ws/src
git clone <repository_url> differential_tracked_navigator
```

2. Build the package:
```bash
cd ~/ros2_ws
colcon build --packages-select differential_tracked_navigator
source install/setup.bash
```

## Configuration

### Maps

Place your map files in the `maps/` folder:
- `map.pgm` - Occupancy grid map image
- `map.yaml` - Map metadata file with resolution, origin, and thresholds

The system will automatically load `maps/map.yaml` by default.

### Vehicle Parameters

Edit `config/navigator_params.yaml` to configure your vehicle:

```yaml
vehicle:
  wheelbase: 0.5                    # Distance between front and rear axles (meters)
  track_width: 0.4                  # Distance between left and right tracks (meters)
  max_linear_velocity: 2.0          # Maximum linear velocity (m/s)
  max_angular_velocity: 1.5         # Maximum angular velocity (rad/s)
```

### Waypoints

Create a `point.txt` file with waypoints in the format:
```
x,y,z
x,y
```

Example:
```
0.0, 0.0, 0.0
2.0, 0.0, 0.0
2.0, -2.0, 0.0
0.0, 0.0, 0.0
```

## Usage

### Basic Launch (with default map)

Launch the navigation system with the default map from maps folder:

```bash
ros2 launch differential_tracked_navigator navigation_launch.py
```

### With Custom Map File

Launch with a specific map:

```bash
ros2 launch differential_tracked_navigator navigation_launch.py \
  map_file:=/path/to/your/map.yaml
```

### Custom Configuration

Launch with custom parameters:

```bash
ros2 launch differential_tracked_navigator navigation_launch.py \
  config_file:=/path/to/custom_params.yaml \
  waypoints_file:=/path/to/waypoints.txt
```

### Simulation Mode

For bag file simulation:

```bash
ros2 launch differential_tracked_navigator navigation_launch.py \
  use_sim_time:=true
  
# In another terminal, play your bag file
ros2 bag play your_data.bag --clock
```

## Topics

### Published Topics

- `/cmd_vel` (geometry_msgs/Twist): Vehicle velocity commands
- `/way_point` (geometry_msgs/PointStamped): Current target waypoint

### Subscribed Topics

- `/state_estimation` (nav_msgs/Odometry): Vehicle localization data
- `/registered_scan` (sensor_msgs/PointCloud2): LiDAR point cloud data
- `/map` (nav_msgs/OccupancyGrid): Map data from map_server

## Navigation Algorithm

The system implements a **Pure Pursuit** control algorithm:

1. **Waypoint Management**: Loads waypoints from file and tracks progress
2. **Path Following**: Calculates steering commands to follow waypoints
3. **Speed Control**: Adjusts velocity based on turning requirements
4. **Obstacle Avoidance**: Stops vehicle when obstacles are detected
5. **Goal Tolerance**: Switches to next waypoint when within tolerance

## Safety Features

- **Obstacle Detection**: Real-time LiDAR-based obstacle detection
- **Emergency Stop**: Immediate stop when obstacles are too close
- **Velocity Limiting**: Respects maximum velocity constraints
- **Error Handling**: Graceful handling of sensor failures

## Monitoring and Visualization

Use RViz to monitor the navigation:

- **Map Display**: Shows loaded occupancy grid map
- **Vehicle Pose**: Current vehicle position and orientation
- **LiDAR Data**: Real-time point cloud visualization
- **Waypoints**: Current target waypoint marker
- **Path**: Planned trajectory visualization

## Troubleshooting

### Common Issues

1. **No waypoints loaded**: Check that `point.txt` exists and has valid format
2. **Map not displaying**: Ensure map file path is correct and map_server is running
3. **Vehicle not moving**: Verify `/state_estimation` topic is publishing valid data
4. **Obstacle detection not working**: Check `/registered_scan` topic data

### Debug Commands

Check topic status:
```bash
ros2 topic list
ros2 topic echo /state_estimation
ros2 topic echo /cmd_vel
```

View node information:
```bash
ros2 node info /differential_tracked_navigator
```

## Parameters Reference

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `vehicle.wheelbase` | double | 0.5 | Wheelbase in meters |
| `vehicle.track_width` | double | 0.4 | Track width in meters |
| `vehicle.max_linear_velocity` | double | 2.0 | Max linear velocity (m/s) |
| `vehicle.max_angular_velocity` | double | 1.5 | Max angular velocity (rad/s) |
| `control.lookahead_distance` | double | 1.0 | Pure pursuit lookahead (m) |
| `control.goal_tolerance` | double | 0.2 | Waypoint tolerance (m) |
| `safety.obstacle_detection_range` | double | 2.0 | Obstacle detection range (m) |
| `safety.safety_stop_distance` | double | 0.5 | Safety stop distance (m) |

## License

This project is licensed under the MIT License.