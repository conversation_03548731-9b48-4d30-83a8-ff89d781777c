#!/usr/bin/env python3

"""
Unified Keyboard Control Script
Combines velocity control and boolean state control into a single interface.
Author: Combined from original keyboard_control.py and keyboard_control_stop.py
"""

import threading
import sys
import termios
import tty

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import <PERSON><PERSON>, Int8


class _Getch:
    """
    Gets a single character from standard input. 
    Does not echo to the screen.
    Cross-platform implementation.
    """

    def __init__(self):
        try:
            self.impl = _GetchWindows()
        except ImportError:
            self.impl = _GetchUnix()

    def __call__(self):
        return self.impl()


class _GetchUnix:
    def __init__(self):
        import tty
        import sys

    def __call__(self):
        import sys
        import tty
        import termios
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        try:
            tty.setraw(sys.stdin.fileno())
            ch = sys.stdin.read(1)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        return ch


class _GetchWindows:
    def __init__(self):
        import msvcrt

    def __call__(self):
        import msvcrt
        return msvcrt.getch()


class UnifiedKeyboardController(Node):
    """
    Unified keyboard controller that handles both velocity commands and boolean states.
    """

    def __init__(self):
        super().__init__('unified_keyboard_controller')
        
        # Publishers
        self.vel_publisher = self.create_publisher(Twist, '/cmd_vel', 1)
        self.obstacle_publisher = self.create_publisher(Bool, '/obstacle_stop', 10)
        self.raise_publisher = self.create_publisher(Int8, '/boom_bucket', 10)
        
        # Velocity control variables
        self.cmd_vel_msg = Twist()
        self.linear_vel = 0.0
        self.angular_vel = 0.0
        self.vel_increment = 0.2
        
        # Boolean state variables
        self.obstacle_stop_state = False
        self.raise_state = 0
        
        # Key state tracking for zero-first behavior
        self.s_key_pressed_before = False  # Track if s key was pressed before
        self.a_key_pressed_before = False  # Track if a key was pressed before
        self.d_key_pressed_before = False  # Track if d key was pressed before
        
        # Timer for continuous velocity publishing
        timer_period = 0.05  # 20 Hz
        self.timer = self.create_timer(timer_period, self.velocity_publish_event)
        
        self.get_logger().info('Unified Keyboard Controller started')
        self.print_instructions()

    def print_instructions(self):
        """Print control instructions"""
        instructions = """
=== UNIFIED KEYBOARD CONTROL ===

VELOCITY CONTROL:
  w: increase linear velocity by {:.1f}
  s: first press sets linear velocity to 0, then decrease by {:.1f}
  a: first press sets angular velocity to 0, then increase by {:.1f} (left turn)
  d: first press sets angular velocity to 0, then decrease by {:.1f} (right turn)
  space: stop (zero velocity and reset key states)

BOOLEAN CONTROL:
  b: toggle obstacle_stop state
  t: toggle raise state (0/1)

SETTINGS:
  +: increase velocity increment
  -: decrease velocity increment
  
CONTROL:
  q: quit and reset all states
  h: show this help

Current velocity increment: {:.2f}

Note: s, a, d keys have 'zero-first' behavior - first press sets to 0, subsequent presses adjust.
        """.format(self.vel_increment, self.vel_increment, self.vel_increment, self.vel_increment, self.vel_increment)
        
        self.get_logger().info(instructions)

    def set_velocity(self, linear, angular):
        """Set velocity values and log them"""
        self.linear_vel = linear
        self.angular_vel = angular
        self.cmd_vel_msg.linear.x = linear
        self.cmd_vel_msg.angular.z = angular
        self.get_logger().info(f"Velocity - Linear: {linear:.2f}, Angular: {angular:.2f}")

    def velocity_publish_event(self):
        """Timer callback to continuously publish velocity"""
        self.vel_publisher.publish(self.cmd_vel_msg)

    def toggle_obstacle_stop(self):
        """Toggle obstacle stop state"""
        self.obstacle_stop_state = not self.obstacle_stop_state
        msg = Bool()
        msg.data = self.obstacle_stop_state
        self.obstacle_publisher.publish(msg)
        self.get_logger().info(f'Obstacle Stop: {self.obstacle_stop_state}')

    def toggle_raise_state(self):
        """Toggle raise state between 0 and 1"""
        self.raise_state = 1 if self.raise_state == 0 else 0
        msg = Int8()
        msg.data = self.raise_state
        self.raise_publisher.publish(msg)
        self.get_logger().info(f'Raise State: {self.raise_state}')

    def reset_all_states(self):
        """Reset all states to initial values"""
        self.get_logger().info('Resetting all states to initial values...')
        
        # Reset velocity
        self.set_velocity(0.0, 0.0)
        
        # Reset boolean states
        self.obstacle_stop_state = False
        self.raise_state = 0
        
        # Reset key state tracking
        self.s_key_pressed_before = False
        self.a_key_pressed_before = False
        self.d_key_pressed_before = False
        
        # Publish reset states
        obstacle_msg = Bool()
        obstacle_msg.data = False
        self.obstacle_publisher.publish(obstacle_msg)
        
        raise_msg = Int8()
        raise_msg.data = 0
        self.raise_publisher.publish(raise_msg)
        
        self.get_logger().info('All states reset')

    def adjust_velocity_increment(self, increase=True):
        """Adjust the velocity increment"""
        if increase:
            self.vel_increment = min(1.0, self.vel_increment + 0.05)
        else:
            self.vel_increment = max(0.05, self.vel_increment - 0.05)
        self.get_logger().info(f'Velocity increment: {self.vel_increment:.2f}')

    def stop_vehicle(self):
        """Immediately stop the vehicle and reset key states"""
        self.set_velocity(0.0, 0.0)
        # Reset key state tracking when stopping
        self.s_key_pressed_before = False
        self.a_key_pressed_before = False
        self.d_key_pressed_before = False

    def get_status_string(self):
        """Get current status as string"""
        return (f"Linear: {self.linear_vel:.2f} | Angular: {self.angular_vel:.2f} | "
                f"Obstacle Stop: {self.obstacle_stop_state} | Raise: {self.raise_state}")


def main(args=None):
    rclpy.init(args=args)
    
    try:
        getch = _Getch()
        controller = UnifiedKeyboardController()
        
        # Start ROS2 node in a separate thread
        thread = threading.Thread(target=rclpy.spin, args=(controller,), daemon=True)
        thread.start()
        
        # Main keyboard input loop
        while rclpy.ok():
            try:
                key = getch()
                
                # Velocity control
                if key == "w":
                    controller.linear_vel += controller.vel_increment
                    controller.set_velocity(controller.linear_vel, controller.angular_vel)
                    # Reset w key doesn't need zero-first behavior, but reset other key states
                    controller.s_key_pressed_before = False
                elif key == "s":
                    # s key: first press sets to 0, subsequent presses decrease
                    if not controller.s_key_pressed_before:
                        # First press: set linear velocity to 0
                        controller.linear_vel = 0.0
                        controller.s_key_pressed_before = True
                        controller.get_logger().info("s key: Linear velocity set to 0 (first press)")
                    else:
                        # Subsequent presses: decrease by increment
                        controller.linear_vel -= controller.vel_increment
                        controller.get_logger().info(f"s key: Linear velocity decreased by {controller.vel_increment}")
                    controller.set_velocity(controller.linear_vel, controller.angular_vel)
                elif key == "a":
                    # a key: first press sets to 0, subsequent presses increase (left turn)
                    if not controller.a_key_pressed_before:
                        # First press: set angular velocity to 0
                        controller.angular_vel = 0.0
                        controller.a_key_pressed_before = True
                        controller.get_logger().info("a key: Angular velocity set to 0 (first press)")
                    else:
                        # Subsequent presses: increase by increment (positive = left turn)
                        controller.angular_vel += controller.vel_increment
                        controller.get_logger().info(f"a key: Angular velocity increased by {controller.vel_increment}")
                    controller.set_velocity(controller.linear_vel, controller.angular_vel)
                    # Reset d key state when a is pressed
                    controller.d_key_pressed_before = False
                elif key == "d":
                    # d key: first press sets to 0, subsequent presses decrease (right turn)
                    if not controller.d_key_pressed_before:
                        # First press: set angular velocity to 0
                        controller.angular_vel = 0.0
                        controller.d_key_pressed_before = True
                        controller.get_logger().info("d key: Angular velocity set to 0 (first press)")
                    else:
                        # Subsequent presses: decrease by increment (negative = right turn)
                        controller.angular_vel -= controller.vel_increment
                        controller.get_logger().info(f"d key: Angular velocity decreased by {controller.vel_increment}")
                    controller.set_velocity(controller.linear_vel, controller.angular_vel)
                    # Reset a key state when d is pressed
                    controller.a_key_pressed_before = False
                elif key == " ":  # Space bar
                    controller.stop_vehicle()
                
                # Boolean control
                elif key == "b":
                    controller.toggle_obstacle_stop()
                elif key == "t":
                    controller.toggle_raise_state()
                
                # Settings
                elif key == "+":
                    controller.adjust_velocity_increment(increase=True)
                elif key == "-":
                    controller.adjust_velocity_increment(increase=False)
                
                # Help and control
                elif key == "h":
                    controller.print_instructions()
                elif key == "q":
                    controller.get_logger().info('Quit requested')
                    break
                elif key == '\x03':  # Ctrl+C
                    break
                
                # Show current status for any key press
                controller.get_logger().debug(controller.get_status_string())
                
            except Exception as e:
                controller.get_logger().error(f'Error processing key input: {e}')
                continue
                
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error: {e}')
    finally:
        if 'controller' in locals():
            controller.reset_all_states()
            # Give time for final messages to be sent
            import time
            time.sleep(0.1)
        
        if rclpy.ok():
            rclpy.shutdown()
        
        if 'thread' in locals():
            thread.join(timeout=1.0)


if __name__ == "__main__":
    main()