#!/usr/bin/env python3
"""
Test script to verify navigation recovery after odometry loss
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
import time

class NavigationRecoveryTest(Node):
    """Test node to verify navigation recovery functionality"""
    
    def __init__(self):
        super().__init__('navigation_recovery_test')
        
        # Publishers
        self.odom_pub = self.create_publisher(Odometry, '/state_estimation', 10)
        
        # Subscribers
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10
        )
        
        # Test state
        self.test_phase = 0
        self.cmd_vel_history = []
        self.test_start_time = time.time()
        
        # Test phases:
        # 0: Normal operation (0-5s)
        # 1: Odometry loss (5-10s) 
        # 2: Odometry recovery (10-15s)
        # 3: Verify navigation resumed (15-20s)
        # 4: Test complete (20s+)
        
        self.test_timer = self.create_timer(0.1, self.run_test)
        self.get_logger().info('Navigation recovery test started')
    
    def cmd_vel_callback(self, msg):
        """Monitor cmd_vel commands"""
        current_time = time.time() - self.test_start_time
        
        # Record all commands with timestamps
        self.cmd_vel_history.append({
            'time': current_time,
            'phase': self.test_phase,
            'linear_x': msg.linear.x,
            'angular_z': msg.angular.z,
            'is_active': abs(msg.linear.x) > 0.01 or abs(msg.angular.z) > 0.01
        })
    
    def run_test(self):
        """Execute test phases"""
        current_time = time.time() - self.test_start_time
        
        if current_time < 5.0:
            # Phase 0: Normal operation
            if self.test_phase != 0:
                self.test_phase = 0
                self.get_logger().info('Phase 0: Normal operation')
            self.publish_normal_odometry()
            
        elif current_time < 10.0:
            # Phase 1: Odometry loss
            if self.test_phase != 1:
                self.test_phase = 1
                self.get_logger().warn('Phase 1: Simulating odometry loss')
            # Don't publish odometry
            
        elif current_time < 15.0:
            # Phase 2: Odometry recovery
            if self.test_phase != 2:
                self.test_phase = 2
                self.get_logger().info('Phase 2: Odometry recovery')
            self.publish_normal_odometry()
            
        elif current_time < 20.0:
            # Phase 3: Verify navigation resumed
            if self.test_phase != 3:
                self.test_phase = 3
                self.get_logger().info('Phase 3: Verifying navigation resumed')
            self.publish_normal_odometry()
            
        else:
            # Phase 4: Test complete
            if self.test_phase != 4:
                self.test_phase = 4
                self.analyze_results()
                self.test_timer.cancel()
    
    def publish_normal_odometry(self):
        """Publish normal odometry data"""
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'map'
        odom_msg.child_frame_id = 'base_link'
        
        # Simple position data (stationary for simplicity)
        odom_msg.pose.pose.position.x = 0.0
        odom_msg.pose.pose.position.y = 0.0
        odom_msg.pose.pose.position.z = 0.0
        
        # Simple orientation (facing forward)
        odom_msg.pose.pose.orientation.w = 1.0
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = 0.0
        
        self.odom_pub.publish(odom_msg)
    
    def analyze_results(self):
        """Analyze test results"""
        self.get_logger().info('=== NAVIGATION RECOVERY TEST RESULTS ===')
        
        # Analyze each phase
        phase_data = {}
        for phase in range(4):
            phase_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == phase]
            active_commands = [cmd for cmd in phase_commands if cmd['is_active']]
            
            phase_data[phase] = {
                'total_commands': len(phase_commands),
                'active_commands': len(active_commands),
                'max_angular': max([abs(cmd['angular_z']) for cmd in phase_commands], default=0.0)
            }
        
        # Phase 0: Should have active navigation
        if phase_data[0]['active_commands'] > 0:
            self.get_logger().info('✓ Phase 0: Navigation was active initially')
        else:
            self.get_logger().warn('⚠ Phase 0: No active navigation detected')
        
        # Phase 1: Should stop during odometry loss
        if phase_data[1]['active_commands'] == 0:
            self.get_logger().info('✓ Phase 1: Vehicle stopped during odometry loss')
        else:
            self.get_logger().warn(f'⚠ Phase 1: {phase_data[1]["active_commands"]} active commands during odometry loss')
        
        # Phase 1: Check for dangerous angular velocities
        if phase_data[1]['max_angular'] > 1.0:
            self.get_logger().error(f'❌ Phase 1: Dangerous angular velocity detected: {phase_data[1]["max_angular"]:.3f} rad/s')
        else:
            self.get_logger().info('✓ Phase 1: No dangerous angular velocities during odometry loss')
        
        # Phase 2: Should resume navigation
        recovery_commands = [cmd for cmd in self.cmd_vel_history if cmd['phase'] == 2 and cmd['time'] > 12.0]
        active_recovery = [cmd for cmd in recovery_commands if cmd['is_active']]
        
        if len(active_recovery) > 0:
            self.get_logger().info('✓ Phase 2: Navigation resumed after odometry recovery')
        else:
            self.get_logger().warn('⚠ Phase 2: Navigation may not have resumed after recovery')
        
        # Phase 3: Should continue navigation
        if phase_data[3]['active_commands'] > 0:
            self.get_logger().info('✓ Phase 3: Navigation continued after recovery')
        else:
            self.get_logger().warn('⚠ Phase 3: Navigation not active in verification phase')
        
        # Overall assessment
        recovery_successful = (
            phase_data[1]['max_angular'] <= 1.0 and  # No dangerous commands during loss
            len(active_recovery) > 0 and             # Navigation resumed
            phase_data[3]['active_commands'] > 0     # Navigation continued
        )
        
        if recovery_successful:
            self.get_logger().info('🎉 OVERALL: Navigation recovery test PASSED')
        else:
            self.get_logger().error('❌ OVERALL: Navigation recovery test FAILED')
        
        # Print summary
        self.get_logger().info(f'Total commands recorded: {len(self.cmd_vel_history)}')
        for phase in range(4):
            self.get_logger().info(
                f'Phase {phase}: {phase_data[phase]["active_commands"]}/{phase_data[phase]["total_commands"]} active commands, '
                f'max angular: {phase_data[phase]["max_angular"]:.3f} rad/s'
            )


def main(args=None):
    rclpy.init(args=args)
    
    test_node = NavigationRecoveryTest()
    
    try:
        rclpy.spin(test_node)
    except KeyboardInterrupt:
        pass
    finally:
        test_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
