#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import <PERSON><PERSON>, Int8
import sys
import termios
import tty
import threading


class KeyboardBoolPublisher(Node):
    def __init__(self):
        super().__init__('keyboard_bool_publisher')
        self.obstacle_publisher = self.create_publisher(Bool, '/obstacle_stop', 10)
        self.raise_publisher = self.create_publisher(Int8, '/raise', 10)
        self.bool_state = False
        self.t_state = 0
        
        self.get_logger().info('Keyboard Bool Publisher started')
        self.get_logger().info('Press "b" to toggle obstacle_stop, "t" to toggle raise, "q" to quit')
        
    def publish_bool(self):
        msg = Bool()
        msg.data = self.bool_state
        self.obstacle_publisher.publish(msg)
        self.get_logger().info(f'Published /obstacle_stop: {self.bool_state}')
    
    def publish_t_state(self):
        msg = Int8()
        msg.data = self.t_state
        self.raise_publisher.publish(msg)
        self.get_logger().info(f'Published /raise: {self.t_state}')


def get_key():
    fd = sys.stdin.fileno()
    old_settings = termios.tcgetattr(fd)
    try:
        tty.setraw(sys.stdin.fileno())
        key = sys.stdin.read(1)
    finally:
        termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
    return key


def main(args=None):
    rclpy.init(args=args)
    
    node = KeyboardBoolPublisher()
    
    def spin_node():
        rclpy.spin(node)
    
    spin_thread = threading.Thread(target=spin_node)
    spin_thread.daemon = True
    spin_thread.start()
    
    try:
        while True:
            key = get_key()
            
            if key == 'b':
                node.bool_state = not node.bool_state
                node.publish_bool()
            elif key == 't':
                node.t_state = 1 if node.t_state == 0 else 0
                node.publish_t_state()
            elif key == 'q':
                node.get_logger().info('Exiting... Setting all states to initial values')
                node.bool_state = False
                node.t_state = 0
                node.publish_bool()
                node.publish_t_state()
                break
            elif key == '\x03':  # Ctrl+C
                break
                
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()