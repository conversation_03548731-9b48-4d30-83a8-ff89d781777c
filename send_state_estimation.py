#!/usr/bin/env python3
"""
脚本用于发送指定位置的 /state_estimation 数据
可以指定 x, y 坐标、发送频率和发送次数
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
import math
import argparse
import time

class StateEstimationSender(Node):
    """发送 state_estimation 数据的节点"""
    
    def __init__(self, x=0.0, y=0.0, yaw=0.0, frequency=10.0, count=None, duration=None):
        super().__init__('state_estimation_sender')
        
        # 参数设置
        self.target_x = x
        self.target_y = y
        self.target_yaw = yaw
        self.frequency = frequency
        self.total_count = count
        self.duration = duration
        
        # 状态变量
        self.sent_count = 0
        self.start_time = time.time()
        
        # 创建发布者
        self.odom_publisher = self.create_publisher(
            Odometry, 
            '/state_estimation', 
            10
        )
        
        # 创建定时器
        timer_period = 1.0 / frequency
        self.timer = self.create_timer(timer_period, self.publish_odometry)
        
        # 打印启动信息
        self.get_logger().info(f'开始发送 state_estimation 数据:')
        self.get_logger().info(f'  位置: x={x:.3f}, y={y:.3f}, yaw={math.degrees(yaw):.1f}°')
        self.get_logger().info(f'  频率: {frequency:.1f} Hz')
        if count is not None:
            self.get_logger().info(f'  发送次数: {count}')
        if duration is not None:
            self.get_logger().info(f'  持续时间: {duration:.1f} 秒')
        self.get_logger().info('按 Ctrl+C 停止发送')
    
    def publish_odometry(self):
        """发布里程计数据"""
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # 检查是否应该停止
        should_stop = False
        
        if self.total_count is not None and self.sent_count >= self.total_count:
            should_stop = True
            stop_reason = f"已发送 {self.sent_count} 次"
        
        if self.duration is not None and elapsed_time >= self.duration:
            should_stop = True
            stop_reason = f"已运行 {elapsed_time:.1f} 秒"
        
        if should_stop:
            self.get_logger().info(f'发送完成: {stop_reason}')
            self.timer.cancel()
            rclpy.shutdown()
            return
        
        # 创建里程计消息
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'map'
        odom_msg.child_frame_id = 'base_link'
        
        # 设置位置
        odom_msg.pose.pose.position.x = self.target_x
        odom_msg.pose.pose.position.y = self.target_y
        odom_msg.pose.pose.position.z = 0.0
        
        # 设置方向 (从 yaw 角度转换为四元数)
        odom_msg.pose.pose.orientation.w = math.cos(self.target_yaw / 2)
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = math.sin(self.target_yaw / 2)
        
        # 设置速度 (静止)
        odom_msg.twist.twist.linear.x = 0.0
        odom_msg.twist.twist.linear.y = 0.0
        odom_msg.twist.twist.linear.z = 0.0
        odom_msg.twist.twist.angular.x = 0.0
        odom_msg.twist.twist.angular.y = 0.0
        odom_msg.twist.twist.angular.z = 0.0
        
        # 发布消息
        self.odom_publisher.publish(odom_msg)
        self.sent_count += 1
        
        # 定期打印状态
        if self.sent_count % max(1, int(self.frequency)) == 0:  # 每秒打印一次
            self.get_logger().info(
                f'已发送 {self.sent_count} 次, 运行时间: {elapsed_time:.1f}s'
            )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='发送指定位置的 state_estimation 数据')
    
    # 位置参数
    parser.add_argument('-x', '--x-pos', type=float, default=0.0,
                       help='X 坐标 (米), 默认: 0.0')
    parser.add_argument('-y', '--y-pos', type=float, default=0.0,
                       help='Y 坐标 (米), 默认: 0.0')
    parser.add_argument('--yaw', type=float, default=0.0,
                       help='偏航角 (弧度), 默认: 0.0')
    parser.add_argument('--yaw-deg', type=float, default=None,
                       help='偏航角 (度), 会覆盖 --yaw 参数')
    
    # 发送参数
    parser.add_argument('-f', '--frequency', type=float, default=10.0,
                       help='发送频率 (Hz), 默认: 10.0')
    parser.add_argument('-c', '--count', type=int, default=None,
                       help='发送次数, 默认: 无限制')
    parser.add_argument('-d', '--duration', type=float, default=None,
                       help='持续时间 (秒), 默认: 无限制')
    
    # 预设位置
    parser.add_argument('--preset', choices=['origin', 'forward', 'left', 'right', 'back'],
                       help='使用预设位置: origin(0,0), forward(1,0), left(0,1), right(0,-1), back(-1,0)')
    
    args = parser.parse_args()
    
    # 处理预设位置
    if args.preset:
        presets = {
            'origin': (0.0, 0.0, 0.0),
            'forward': (1.0, 0.0, 0.0),
            'left': (0.0, 1.0, 0.0),
            'right': (0.0, -1.0, 0.0),
            'back': (-1.0, 0.0, math.pi)
        }
        args.x_pos, args.y_pos, args.yaw = presets[args.preset]
        print(f'使用预设位置 "{args.preset}": x={args.x_pos}, y={args.y_pos}, yaw={args.yaw}')
    
    # 处理角度转换
    if args.yaw_deg is not None:
        args.yaw = math.radians(args.yaw_deg)
    
    # 参数验证
    if args.frequency <= 0:
        print('错误: 频率必须大于 0')
        return
    
    if args.count is not None and args.count <= 0:
        print('错误: 发送次数必须大于 0')
        return
    
    if args.duration is not None and args.duration <= 0:
        print('错误: 持续时间必须大于 0')
        return
    
    # 初始化 ROS2
    rclpy.init()
    
    try:
        # 创建并运行节点
        sender = StateEstimationSender(
            x=args.x_pos,
            y=args.y_pos, 
            yaw=args.yaw,
            frequency=args.frequency,
            count=args.count,
            duration=args.duration
        )
        
        rclpy.spin(sender)
        
    except KeyboardInterrupt:
        print('\n用户中断，停止发送')
    except Exception as e:
        print(f'错误: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
