#!/usr/bin/env python3
"""
Test script to simulate odometry loss and verify control stability
"""

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import Twist
import time
import threading

class OdometryLossTest(Node):
    """Test node to simulate odometry loss scenarios"""
    
    def __init__(self):
        super().__init__('odometry_loss_test')
        
        # Publishers
        self.odom_pub = self.create_publisher(Odometry, '/state_estimation', 10)
        
        # Subscribers
        self.cmd_vel_sub = self.create_subscription(
            Twist, '/cmd_vel', self.cmd_vel_callback, 10
        )

        # Monitor navigation status
        self.navigation_active_periods = []
        self.last_cmd_vel_time = None
        
        # Test state
        self.publishing_odom = True
        self.cmd_vel_history = []
        self.max_angular_vel_seen = 0.0
        
        # Start test sequence
        self.test_timer = self.create_timer(0.1, self.test_sequence)
        self.test_start_time = time.time()
        
        self.get_logger().info('Odometry loss test started')
    
    def cmd_vel_callback(self, msg):
        """Monitor cmd_vel for dangerous commands and navigation activity"""
        current_time = time.time() - self.test_start_time

        # Record command
        self.cmd_vel_history.append({
            'time': current_time,
            'linear_x': msg.linear.x,
            'angular_z': msg.angular.z
        })

        # Track navigation activity (non-zero commands indicate active navigation)
        is_active = abs(msg.linear.x) > 0.01 or abs(msg.angular.z) > 0.01
        if is_active:
            self.last_cmd_vel_time = current_time

        # Track maximum angular velocity
        abs_angular = abs(msg.angular.z)
        if abs_angular > self.max_angular_vel_seen:
            self.max_angular_vel_seen = abs_angular

        # Check for dangerous commands
        if abs_angular > 1.5:  # Close to max angular velocity
            self.get_logger().warn(
                f'HIGH ANGULAR VELOCITY DETECTED: {msg.angular.z:.3f} rad/s at t={current_time:.1f}s'
            )

        # Log current command
        if len(self.cmd_vel_history) % 10 == 0:  # Log every 10th command
            self.get_logger().info(
                f't={current_time:.1f}s: linear={msg.linear.x:.3f}, angular={msg.angular.z:.3f}'
            )
    
    def test_sequence(self):
        """Execute test sequence"""
        current_time = time.time() - self.test_start_time
        
        if current_time < 5.0:
            # Phase 1: Normal operation (0-5s)
            if self.publishing_odom:
                self.publish_normal_odometry()
        
        elif current_time < 10.0:
            # Phase 2: Odometry loss (5-10s)
            if self.publishing_odom:
                self.get_logger().warn('SIMULATING ODOMETRY LOSS')
                self.publishing_odom = False
        
        elif current_time < 15.0:
            # Phase 3: Odometry recovery (10-15s)
            if not self.publishing_odom:
                self.get_logger().info('SIMULATING ODOMETRY RECOVERY')
                self.publishing_odom = True
            self.publish_normal_odometry()
        
        else:
            # Phase 4: Test complete (15s+)
            self.print_test_results()
            self.test_timer.cancel()
    
    def publish_normal_odometry(self):
        """Publish normal odometry data"""
        odom_msg = Odometry()
        odom_msg.header.stamp = self.get_clock().now().to_msg()
        odom_msg.header.frame_id = 'map'
        odom_msg.child_frame_id = 'base_link'
        
        # Simple position data
        odom_msg.pose.pose.position.x = 1.0
        odom_msg.pose.pose.position.y = 1.0
        odom_msg.pose.pose.position.z = 0.0
        
        # Simple orientation (facing forward)
        odom_msg.pose.pose.orientation.w = 1.0
        odom_msg.pose.pose.orientation.x = 0.0
        odom_msg.pose.pose.orientation.y = 0.0
        odom_msg.pose.pose.orientation.z = 0.0
        
        self.odom_pub.publish(odom_msg)
    
    def print_test_results(self):
        """Print test results"""
        self.get_logger().info('=== ODOMETRY LOSS TEST RESULTS ===')
        self.get_logger().info(f'Total commands recorded: {len(self.cmd_vel_history)}')
        self.get_logger().info(f'Maximum angular velocity seen: {self.max_angular_vel_seen:.3f} rad/s')
        
        # Check for dangerous commands during odometry loss (5-10s)
        dangerous_commands = [
            cmd for cmd in self.cmd_vel_history 
            if 5.0 <= cmd['time'] <= 10.0 and abs(cmd['angular_z']) > 1.0
        ]
        
        if dangerous_commands:
            self.get_logger().error(f'FOUND {len(dangerous_commands)} DANGEROUS COMMANDS DURING ODOMETRY LOSS!')
            for cmd in dangerous_commands[:5]:  # Show first 5
                self.get_logger().error(
                    f'  t={cmd["time"]:.1f}s: angular={cmd["angular_z"]:.3f} rad/s'
                )
        else:
            self.get_logger().info('✓ NO DANGEROUS COMMANDS DETECTED DURING ODOMETRY LOSS')
        
        # Check recovery behavior
        recovery_commands = [
            cmd for cmd in self.cmd_vel_history 
            if 10.0 <= cmd['time'] <= 12.0
        ]
        
        if recovery_commands:
            max_recovery_angular = max(abs(cmd['angular_z']) for cmd in recovery_commands)
            self.get_logger().info(f'Max angular velocity during recovery: {max_recovery_angular:.3f} rad/s')
            
            if max_recovery_angular > 1.5:
                self.get_logger().warn('HIGH ANGULAR VELOCITY DURING RECOVERY - CHECK PID RESET')
            else:
                self.get_logger().info('✓ RECOVERY BEHAVIOR LOOKS GOOD')

        # Check if navigation resumed after recovery
        post_recovery_commands = [
            cmd for cmd in self.cmd_vel_history
            if 12.0 <= cmd['time'] <= 15.0 and (abs(cmd['linear_x']) > 0.01 or abs(cmd['angular_z']) > 0.01)
        ]

        if post_recovery_commands:
            self.get_logger().info(f'✓ NAVIGATION RESUMED: {len(post_recovery_commands)} active commands after recovery')
        else:
            self.get_logger().warn('⚠ NAVIGATION MAY NOT HAVE RESUMED AFTER RECOVERY')

        self.get_logger().info('=== TEST COMPLETE ===')

        # Provide recommendations
        if self.max_angular_vel_seen < 0.1:
            self.get_logger().info('💡 RECOMMENDATION: System appears very conservative, consider tuning PID gains')
        elif self.max_angular_vel_seen > 1.5:
            self.get_logger().warn('⚠ RECOMMENDATION: High angular velocities detected, review safety limits')


def main(args=None):
    rclpy.init(args=args)
    
    test_node = OdometryLossTest()
    
    try:
        rclpy.spin(test_node)
    except KeyboardInterrupt:
        pass
    finally:
        test_node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
